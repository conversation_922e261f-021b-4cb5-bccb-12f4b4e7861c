import { defineStore } from 'pinia';
import { storage } from '@/utils/Storage';
import router from "@/router";
import { login, logout, findUserResource, tokenAuth, findUserSimpleInfoByToken, queryRoleListByToken } from '@/api/system/user';
import { isDevMode } from '@/utils/env';
import { clearCsrfCookies } from '@/utils/axios';

// 角色常量
export const ROLES = {
  SYS_ADMIN: 'sys_admin',
  AREA_ADMIN: 'area_admin',
  COUNTRY_ADMIN: 'country_admin',
  VIEW: 'view',
  DTSE: 'dtse',
};

// 定义接口响应类型
interface ApiResponse<T = any> {
  resultCode: number | string;
  info: string;
  data: T;
}

// 定义菜单项类型
export interface MenuItem {
  resourceId: string;
  resoNameEn: string;
  resoNameCn: string;
  resoType: number;
  mobileFlag: string;
  orderNo: number;
  resoWebUrl: string;
  resoMobileUrl: string;
  resoParentId: string;
  resoDescCn: string;
  resoDescEn: string;
  enableFlag: string;
  resoRank: number;
  isLook: string;
  dataFlag: string;
  isPublicResource: string;
  children?: MenuItem[];
}

// 用户信息类型
export interface UserInfo {
  account: string;
  userId: string;
  cnNameEmpNo: string;
  enNameEmpNo: string;
  currentLanguage: string;
  currentRole: string;
  employeeNumber: string;
  email: string;
  minDepartmentCn: string;
  minDepartmentEn: string;
  minDepartmentCode: string;
  l3DepartmentCn: string;
  l3DepartmentEn: string;
  resourceVoList?: MenuItem[];
}

// 定义角色类型
export interface RoleInfo {
  roleName?: string;
  roleDesc?: string;
  roleENName?: string;
  roleId?: string;
  roleCNName?: string;
  appId?: string;
  dimensionGroup?: any[];
}

// 开发模式下的模拟用户信息
const mockUserInfo: UserInfo = {
  account: 'dev_user',
  userId: 'dev_001',
  cnNameEmpNo: '开发测试用户',
  enNameEmpNo: 'Dev Test User',
  currentLanguage: 'zh-CN',
  currentRole: ROLES.SYS_ADMIN,
  employeeNumber: 'dev001',
  email: '邮箱地址',
  minDepartmentCn: '开发部门',
  minDepartmentEn: 'Development',
  minDepartmentCode: 'DEV',
  l3DepartmentCn: '开发部门',
  l3DepartmentEn: 'Development'
};

// 开发模式下的模拟角色列表
const mockRoleList: RoleInfo[] = [
  {
    roleId: '1',
    roleName: ROLES.SYS_ADMIN,
    roleENName: ROLES.SYS_ADMIN,
    roleCNName: '系统管理员',
    roleDesc: '系统管理员角色'
  }
];

// 开发模式下的模拟菜单权限
const mockMenuList: MenuItem[] = [
  {
    resourceId: 'MyTodo',
    resoNameEn: 'My Todo',
    resoNameCn: '我的待办',
    resoType: 1,
    mobileFlag: '1',
    orderNo: 1,
    resoWebUrl: '/todo',
    resoMobileUrl: '',
    resoParentId: '',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 1,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'SourceSceneManage',
    resoNameEn: 'Source Scene Manage',
    resoNameCn: '源声场景管理',
    resoType: 1,
    mobileFlag: '1',
    orderNo: 2,
    resoWebUrl: '/source-scene-manage',
    resoMobileUrl: '',
    resoParentId: '',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 2,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'ImportSourceData',
    resoNameEn: 'Import Source Data',
    resoNameCn: '导入源声数据',
    resoType: 2,
    mobileFlag: '1',
    orderNo: 1,
    resoWebUrl: '',
    resoMobileUrl: '',
    resoParentId: 'SourceSceneManage',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 1,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'ExportSourceData',
    resoNameEn: 'Export Source Data',
    resoNameCn: '导出源声数据',
    resoType: 2,
    mobileFlag: '1',
    orderNo: 2,
    resoWebUrl: '',
    resoMobileUrl: '',
    resoParentId: 'SourceSceneManage',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 2,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'ListSettingData',
    resoNameEn: 'List Setting',
    resoNameCn: '列表设置',
    resoType: 2,
    mobileFlag: '1',
    orderNo: 3,
    resoWebUrl: '',
    resoMobileUrl: '',
    resoParentId: 'SourceSceneManage',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 3,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'IssuesOpinionManage',
    resoNameEn: 'Issues Opinion Manage',
    resoNameCn: '问题管理',
    resoType: 1,
    mobileFlag: '1',
    orderNo: 3,
    resoWebUrl: '/issues-opinion-manage',
    resoMobileUrl: '',
    resoParentId: '',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 3,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'DataDashboard',
    resoNameEn: 'Data Dashboard',
    resoNameCn: '数据看板',
    resoType: 1,
    mobileFlag: '1',
    orderNo: 4,
    resoWebUrl: '/dataview',
    resoMobileUrl: '',
    resoParentId: '',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 4,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'StatisticalDashboard',
    resoNameEn: 'Statistical Dashboard',
    resoNameCn: '问题统计看板',
    resoType: 2,
    mobileFlag: '1',
    orderNo: 1,
    resoWebUrl: '/statistical-dashboard',
    resoMobileUrl: '',
    resoParentId: 'DataDashboard',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 1,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  },
  {
    resourceId: 'MetaManager',
    resoNameEn: 'Meta Manager',
    resoNameCn: '元数据管理',
    resoType: 2,
    mobileFlag: '1',
    orderNo: 2,
    resoWebUrl: '/meta-manager',
    resoMobileUrl: '',
    resoParentId: 'DataDashboard',
    resoDescCn: '',
    resoDescEn: '',
    enableFlag: '1',
    resoRank: 2,
    isLook: '1',
    dataFlag: '1',
    isPublicResource: '1'
  }
];

// 用户Store
export const useUserStore = defineStore({
  id: 'user',
  state: () => ({
    menuPermissions: [] as MenuItem[],
    isDtse: false,
    memberId: null as string | null,
    memberDeleteTime: null as string | null,
    displayNameCn: '',
    currentName: '',
    userInfo: {} as UserInfo,
    menuList: [] as MenuItem[],
    role: '',
    timer: null as any,
    triggerTimer: null as any,
    roleSet: new Set<string>(),
    roleList: [] as RoleInfo[],
    $router: router,
  }),

  getters: {
    getMenuPermissions(): MenuItem[] {
      return this.menuPermissions;
    },
    isAdmin(state) {
      return state.role === ROLES.SYS_ADMIN || state.role === ROLES.AREA_ADMIN;
    },
    isSysAdmin(state) {
      return state.role === ROLES.SYS_ADMIN;
    },
    isDtseUser(state) {
      return state.role === ROLES.DTSE;
    },
    // 获取角色列表
    getRoleList(state): RoleInfo[] {
      return state.roleList;
    },
    // 检查用户是否拥有指定角色
    hasRole(state) {
      return (role: string): boolean => {
        return state.roleSet.has(role);
      };
    },
  },

  actions: {
    // 设置用户信息
    setUserInfo(userInfo: Partial<UserInfo>) {
      this.userInfo = {
        ...this.userInfo,
        ...userInfo
      };

      // 更新显示名称
      if (userInfo.cnNameEmpNo) {
        this.displayNameCn = userInfo.cnNameEmpNo;
      }

      if (userInfo.cnNameEmpNo || userInfo.enNameEmpNo) {
        this.currentName = userInfo.cnNameEmpNo || userInfo.enNameEmpNo || '';
      }
    },

    // 登录
    async login(params: { account: string; password: string }) {
      // 开发模式下模拟登录成功
      if (isDevMode()) {
        // 设置登录表单中的用户名为显示名称
        const customUserInfo = {
          ...mockUserInfo,
          account: params.account,
          cnNameEmpNo: params.account,
          enNameEmpNo: params.account,
        };

        this.setUserInfo(customUserInfo);
        await this.getUserRoleList();
        await this.getUserPermissions();

        return { status: '200', message: 'success' };
      }

      try {
        const res = await login(params);
        if (res) {
          // 登录成功后初始化用户数据
          await this.initUserData();
          return { status: '200', message: 'success' };
        }
        return { status: '400', message: '登录失败' };
      } catch (error) {
        console.error('登录失败:', error);
        return { status: '400', message: '登录失败' };
      }
    },

    // 检查用户是否登录
    async checkLogin() {
      // 开发模式下直接返回成功
      if (isDevMode()) {
        return { resultCode: 0, message: 'success' };
      }

      try {
        const res = await tokenAuth();
        return res
      } catch (error) {
        return false;
      }
    },

    // 获取用户信息
    async getUserInfo() {
      // 开发模式下返回模拟用户信息
      if (isDevMode()) {
        // 如果已经设置了用户信息，则不覆盖
        if (!this.userInfo.userId) {
          this.setUserInfo(mockUserInfo);
        }
        return this.userInfo;
      }

      try {
        const res = await findUserSimpleInfoByToken();
        if (res) {
          this.setUserInfo(res);
          return res;
        }
        return null;
      } catch (error) {
        console.error('获取用户信息失败:', error);
        return null;
      }
    },

    // 获取用户角色列表
    async getUserRoleList() {
      // 开发模式下返回模拟角色列表
      if (isDevMode()) {
        this.roleList = mockRoleList;
        this.setRoleInfo(mockRoleList);
        return mockRoleList;
      }

      try {
        const res = await queryRoleListByToken();
        if (res) {
          this.roleList = res.roleList;
          // 使用角色列表设置角色信息
          this.setRoleInfo(res.roleList);
          return res.roleList;
        }
        return [];
      } catch (error) {
        console.error('获取用户角色列表失败:', error);
        return [];
      }
    },

    // 获取用户权限
    async getUserPermissions() {
      // 开发模式下返回模拟菜单权限
      if (isDevMode()) {
        this.menuList = mockMenuList;
        this.processMenuPermissions(mockMenuList);
        return mockMenuList;
      }

      try {
        const res = await findUserResource();
        if (res) {
          this.menuList = res.resourceVoList;
          // 处理菜单权限
          this.processMenuPermissions(res.resourceVoList);
          return res.resourceVoList;
        }
        return [];
      } catch (error) {
        console.error('获取用户权限失败:', error);
        return [];
      }
    },

    // 初始化用户数据
    async initUserData() {
      try {
        // 获取用户基本信息
        await this.getUserInfo();

        // 获取用角色列表
        await this.getUserRoleList();

        // 获取用户权限菜单
        await this.getUserPermissions();

        return {
          userInfo: this.userInfo,
          roleList: this.roleList,
          menuList: this.menuList,
          role: this.role
        };
      } catch (error) {
        console.error('初始化用户数据失败:', error);
        return null;
      }
    },

    // 菜单处理
    processMenuPermissions(menuList: MenuItem[]) {
      const menus = menuList.filter(item => item.resoType === 1 || item.resoType === 2);

      const menuTree: MenuItem[] = [];
      const menuMap = new Map<string, MenuItem>();

      // 首先创建所有菜单项并存储在map中
      menus.forEach(item => {
        menuMap.set(item.resourceId, {
          ...item,
          children: []
        });
      });

      // 构建菜单树
      menus.forEach(item => {
        const menuItem = menuMap.get(item.resourceId);

        if (menuItem) {
          if (item.resoParentId && menuMap.has(item.resoParentId)) {
            // 如果有父级菜单，则添加为子菜单
            const parentMenu = menuMap.get(item.resoParentId);
            if (parentMenu && parentMenu.children) {
              parentMenu.children.push(menuItem);
            }
          } else {
            // 如果没有父级菜单，则为顶级菜单
            menuTree.push(menuItem);
          }
        }
      });

      this.menuPermissions = menuTree;
      return menuTree;
    },

    // 设置角色信息
    setRoleInfo(roleList: RoleInfo[]) {
      if (!roleList || roleList.length === 0) {
        return;
      }

      // 清空角色集合
      this.roleSet = new Set();

      // 遍历角色列表，添加到角色集合
      roleList.forEach(item => {
        const roleKey = item.roleENName || item.roleName || item.roleCNName;
        if (roleKey) {
          this.roleSet.add(roleKey);
        }
      });

      // 设置主要角色
      if (roleList.some(x => x.roleENName === ROLES.SYS_ADMIN || x.roleName === ROLES.SYS_ADMIN)) {
        this.role = ROLES.SYS_ADMIN;
      } else if (roleList.some(x => x.roleENName === ROLES.AREA_ADMIN || x.roleName === ROLES.AREA_ADMIN)) {
        this.role = ROLES.AREA_ADMIN;
      } else if (roleList.some(x => x.roleENName === ROLES.COUNTRY_ADMIN || x.roleName === ROLES.COUNTRY_ADMIN)) {
        this.role = ROLES.COUNTRY_ADMIN;
      } else if (roleList.some(x => x.roleENName === ROLES.VIEW || x.roleName === ROLES.VIEW)) {
        this.role = ROLES.VIEW;
      } else if (roleList.some(x => x.roleENName === ROLES.DTSE || x.roleName === ROLES.DTSE)) {
        this.role = ROLES.DTSE;
        this.isDtse = true;
      } else {
        // 默认设置为查看角色
        this.role = ROLES.VIEW;
      }
    },

    // 清除定时器
    clearTimers() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      if (this.triggerTimer) {
        clearInterval(this.triggerTimer);
        this.triggerTimer = null;
      }
    },

    // 登出
    async logout() {
      // 开发模式下直接刷新页面
      if (isDevMode()) {
        // 清除CSRF相关cookie
        clearCsrfCookies();
        window.location.reload();
        return { success: true };
      }

      try {
        const res = await logout();
        // 无论登出API是否成功，都清除CSRF相关cookie
        clearCsrfCookies();
        if (res && res.w3LogoutUrl) {
          window.location.href = res.w3LogoutUrl;
        }
        return res;
      } catch (error) {
        console.error('登出失败:', error);
        // 即使登出失败，也清除CSRF相关cookie
        clearCsrfCookies();
        return null;
      }
    },

    // 跳转到403页面
    goVerify() {
      router.push('/403');
    }
  }
});

