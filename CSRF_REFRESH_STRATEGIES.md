# CSRF Token 自动刷新策略

## 概述

为了解决门户网站重新登录导致 `wo_auth` 改变，从而使 CSRF Token 失效的问题，我们实现了多种自动刷新策略。

## 实现方案

### 方案1：请求重试机制（已实现）
当收到 `40019` 错误码时，自动重新获取 CSRF Token 并重试原始请求。

### 方案2：定时刷新机制（新增）
预防性地定期刷新 CSRF Token，避免在关键请求时遇到 token 失效。

## 刷新策略

### 1. 固定间隔刷新 (Fixed Interval)
```javascript
// 每5分钟刷新一次
configureCSRFRefresh('conservative'); // 2分钟间隔
```

**优点**：
- 简单可靠
- 确保 token 始终有效

**缺点**：
- 可能产生不必要的请求
- 固定间隔可能不够灵活

### 2. 智能刷新 (Smart Refresh)
```javascript
// 根据 token 过期时间智能刷新
configureCSRFRefresh('economical');
```

**特点**：
- 在 token 过期前1-2分钟自动刷新
- 减少不必要的请求
- 根据实际过期时间调整

### 3. 页面可见性刷新 (Visibility Refresh)
```javascript
// 页面重新可见时检查并刷新
configureCSRFRefresh('onDemand');
```

**特点**：
- 用户切换回页面时检查 token 状态
- 适合长时间后台运行的场景
- 节省服务器资源

### 4. 混合策略 (Hybrid)
```javascript
// 结合多种策略（默认）
configureCSRFRefresh('balanced');
```

**特点**：
- 固定间隔 + 智能检查 + 可见性检查
- 最大化可靠性
- 适合大多数场景

## 使用方法

### 快速配置
```javascript
import { configureCSRFRefresh } from '@/utils/axios';

// 选择预设策略
configureCSRFRefresh('balanced');    // 平衡策略（推荐）
configureCSRFRefresh('conservative'); // 保守策略（频繁刷新）
configureCSRFRefresh('economical');   // 节约策略（按需刷新）
configureCSRFRefresh('onDemand');     // 仅在需要时刷新
```

### 自定义配置
```javascript
import { setCustomCSRFRefresh } from '@/utils/axios';

setCustomCSRFRefresh({
  strategy: 'fixed',
  fixedInterval: 3 * 60 * 1000, // 3分钟刷新一次
});

// 或者混合策略
setCustomCSRFRefresh({
  strategy: 'hybrid',
  fixedInterval: 10 * 60 * 1000, // 10分钟固定刷新
  smartRefresh: {
    checkInterval: 2 * 60 * 1000,     // 2分钟检查一次
    refreshBeforeExpiry: 3 * 60 * 1000, // 过期前3分钟刷新
  },
  visibilityRefresh: {
    refreshAfterHidden: 5 * 60 * 1000, // 隐藏5分钟后刷新
  },
});
```

## 策略选择建议

### 高频使用场景
- **推荐**：`balanced` 或 `conservative`
- **原因**：确保 token 始终有效，避免用户操作中断

### 低频使用场景
- **推荐**：`economical` 或 `onDemand`
- **原因**：减少不必要的服务器请求

### 后台长时间运行
- **推荐**：`onDemand` + 适当的固定间隔
- **原因**：页面不可见时减少请求，可见时及时刷新

### 移动端或弱网络环境
- **推荐**：`economical`
- **原因**：减少网络请求，节省流量

## 监控和调试

### 查看当前配置
```javascript
import { csrfRefreshManager } from '@/utils/CSRFRefreshConfig';

console.log('当前刷新策略:', csrfRefreshManager.config);
```

### 手动触发刷新
```javascript
import { getCsrfToken } from '@/utils/axios';

// 手动刷新 token
await getCsrfToken();
```

### 停止自动刷新
```javascript
import { csrfRefreshManager } from '@/utils/CSRFRefreshConfig';

// 停止所有自动刷新
csrfRefreshManager.stop();

// 重新启动
csrfRefreshManager.start();
```

## 性能考虑

1. **网络请求频率**：根据业务需求选择合适的刷新间隔
2. **服务器负载**：避免过于频繁的 token 刷新请求
3. **用户体验**：确保关键操作时 token 有效
4. **电池消耗**：移动端考虑定时器对电池的影响

## 故障处理

1. **刷新失败**：自动重试机制会在下次请求时处理
2. **网络异常**：不会影响现有 token 的使用
3. **服务器错误**：记录日志，不中断用户操作

## 最佳实践

1. **生产环境**：使用 `balanced` 策略
2. **开发环境**：可以使用 `conservative` 策略便于调试
3. **监控日志**：关注 token 刷新的成功率和频率
4. **用户反馈**：根据实际使用情况调整策略
