import { ewpService as service } from '@/utils/axios';

export const fetchFixVersionTable = async (opinionIssueId) => {
  try {
    const response = await service.get(`/confirmResolveVersion/view?opinionIssueId=${opinionIssueId}`, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response;
  } catch (error) {
      throw error;
  }
}

// 保存解决计划
export const saveFixVersion = async (params) => {
  try {
    const response = await service.put('/confirmResolveVersion/save', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response;
  } catch (error) {
      throw error;
  }
}

// 完成/转单
export const submitFixVersion = async ( params ) => {
  try {
    const response = await service.put('/confirmResolveVersion/submit', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response;
  } catch (error) {
      throw error;
  }
}