<template>
  <div>
    <el-row :gutter="32">
      <el-col class="detail-container" :span="21">
        <el-form ref="formRef" :model="editForm">
          <div
            v-for="itemArr in detailData"
            :key="getAnchorId(itemArr[0].cName)"
            :id="getAnchorId(itemArr[0].type)"
            class="detail-box"
          >
            <div
              class="edit-box"
              v-if="getAnchorId(itemArr[0].type) === 'basicInfo'"
            >
              <template v-if="hasEditPermission">
                <span v-if="!isEdit" @click="editHandle" class="primary-font"
                  >编辑</span
                >
                <div v-else>
                  <span @click="isEdit = false" class="primary-font">取消</span>
                  <span
                    @click="saveDetail"
                    class="primary-font"
                    style="margin-left: 16px"
                    >保存</span
                  >
                </div>
              </template>
            </div>
            <div class="detail-type">{{ itemArr[0].type }}</div>
            <el-row>
              <el-col
                v-for="item in itemArr"
                :span="6"
                :key="item.type"
                class="detail-item"
              >
                <div class="detail-title fontSize-14">{{ item.title }}</div>
                <el-form-item
                  v-if="isEdit && CanEditKey.includes(item.prop)"
                  style="width: 80%"
                  :rules="EditComponentsData[item.prop].rules"
                  :prop="item.prop"
                >
                  <el-select
                    v-model="editForm[item.prop]"
                    :placeholder="'请选择' + item.title"
                    v-if="EditComponentsData[item.prop].component === 'Select'"
                  >
                    <el-option
                      v-for="o in EditComponentsData[item.prop].option"
                      :key="o.value"
                      :label="o.label"
                      :value="o.value"
                    />
                  </el-select>
                  <el-input
                    v-model.trim="editForm[item.prop]"
                    :placeholder="'请输入' + item.title"
                    v-if="EditComponentsData[item.prop].component === 'Input'"
                    :type="EditComponentsData[item.prop].type || 'text'"
                  />
                </el-form-item>
                <el-text
                  line-clamp="1"
                  :title="item.value"
                  class="detail-value fontSize-14"
                  v-else
                >
                  {{ item.value || "---" }}
                </el-text>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-col>
      <el-col :span="3">
        <el-anchor
          type="underline"
          :duration="500"
          @click="handleClickAnchor"
          :get-container="getContainer"
          @change="changeStep"
        >
          <el-anchor-link
            v-for="item in anchorIdObject"
            :key="item.eName"
            :href="'#' + item.eName"
            :title="item.cName"
          ></el-anchor-link>
        </el-anchor>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import {
  onMounted,
  onBeforeUnmount,
  ref,
  defineProps,
  toRefs,
  reactive,
  computed,
} from "vue";
import { useEventBus } from "@/hooks/useEventBus";
import { EditComponentsData, CanEditKey } from "./index";
import { opinionIssueUpdate } from "@/api/view/optionDetail/detail";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import { useUserStore } from "@/store";
const router = useRoute();
const eventBus = useEventBus();
const props = defineProps({
  detailData: {
    default: () => [],
    required: true,
  },
});

const userStore = useUserStore();
const userInfo = userStore;
const { detailData } = toRefs(props);
const anchorIdObject = [
  {
    cName: "基本信息",
    eName: "basicInfo",
  },
  {
    cName: "问题处理信息",
    eName: "problemHandlingInfo",
  },
  {
    cName: "责任人信息",
    eName: "responsiblePersonInfo",
  },
  {
    cName: "关键时间点",
    eName: "criticalTimePoint",
  },
  {
    cName: "应用信息",
    eName: "appInfo",
  },
];
// 通用权限检查函数
const checkResourcePermission = (resourceId: string) => {
  if (!resourceId || !userStore.menuList) return false;
  // 检查菜单列表中是否包含指定资源ID
  return userStore.menuList.some((menu) => menu.resourceId === resourceId);
};
// 判断是否有导出权限
const hasEditPermission = computed(() => {
  return checkResourcePermission("ModifyPublicOpinionInfo");
});
const getAnchorId = (cName: string) => {
  const cur = anchorIdObject.find((v) => v.cName === cName);
  return (cur || {}).eName;
};
const isEdit = ref(false);
const editForm = reactive({});
let anchorBox: Element | null = null; // 锚点容器
const fixedAnchor = (scrollTop: number) => {
  const anchorClassName = anchorBox?.className;
  const className = "fixed-anchor";
  if (scrollTop >= 65) {
    if (!anchorClassName?.includes(className)) {
      anchorBox?.classList.add(className);
    }
  } else {
    if (anchorClassName?.includes(className)) {
      anchorBox?.classList.remove(className);
    }
  }
};

const handleClickAnchor = (e, link) => {
  e.preventDefault();
  if (link) {
    const ele = document.querySelector(link);
    ele &&
      ele.scrollIntoView({
        behavior: "smooth",
        block: "start",
        alignToTop: "false",
      });
  }
};

const getContainer = () => {
  return document.querySelector(".detail-container");
};

const formRef = ref(); // 编辑表单
const editHandle = () => {
  // 默认数据回填
  detailData.value.forEach((v: []) => {
    v.forEach((data) => {
      if (CanEditKey.includes(data.prop)) {
        editForm[data.prop] = data.defaultValue;
      }
    });
  });
  isEdit.value = true;
};
const saveDetail = async () => {
  formRef.value.validate(async (valid, fields) => {
    if (!valid) return;
    try {
      const res = await opinionIssueUpdate({
        ...editForm,
        opinionIssueId: router.query.id,
      });
      // 重新请求数据
      eventBus.emit("getIssueInfo", () => {
        isEdit.value = false;
      });
      ElMessage({
        type: "info",
        message: "详情更新成功",
      });
    } catch (error) {
      isEdit.value = false;
      ElMessage({
        type: "error",
        message: "详情更新失败",
      });
    }
  });
};

const changeStep = () => {
  // 切换锚点清除之前的样式
  document.querySelector('.el-anchor__link.is-active')?.classList.remove('is-active')
}

onMounted(() => {
  anchorBox = document.querySelector(".el-anchor");
  eventBus.on("fixedAnchorPoint", (scrollTop: number) => {
    fixedAnchor(scrollTop);
  });
  // 看不懂原来的代码，用原生 JS 实现同步锚点，待优化
  const allAnchorDOM = document.querySelectorAll(".el-anchor__link");
  const basicInfoDOM = document.querySelector(
    '.el-anchor__link[href="#basicInfo"]'
  );
  const problemHandlingInfoDOM = document.querySelector(
    '.el-anchor__link[href="#problemHandlingInfo"]'
  );
  const responsiblePersonInfoDOM = document.querySelector(
    '.el-anchor__link[href="#responsiblePersonInfo"]'
  );
  const criticalTimePointDOM = document.querySelector(
    '.el-anchor__link[href="#criticalTimePoint"]'
  );
  const anchorMarkerDOM = document.querySelector(".el-anchor__marker");
  document
    .querySelector(".el-scrollbar__wrap")
    .addEventListener("scroll", (event) => {
      const scrollTop = document.querySelector(
        "#app > div > div.main-container > div > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default"
      )?.scrollTop;
      allAnchorDOM.forEach((item) => {
        if (item.classList.contains("is-active")) {
          item.classList.remove("is-active");
        }
      });
      if (scrollTop < 525) {
        basicInfoDOM.classList.add("is-active");
        anchorMarkerDOM.style.top = "0px";
      } else if (scrollTop < 825) {
        problemHandlingInfoDOM.classList.add("is-active");
        anchorMarkerDOM.style.top = "32px";
      } else if (scrollTop < 1120) {
        responsiblePersonInfoDOM.classList.add("is-active");
        anchorMarkerDOM.style.top = "64px";
      } else {
        criticalTimePointDOM.classList.add("is-active");
        anchorMarkerDOM.style.top = "96px";
      }
    });
  // 第一次默认第一个节点选中
  document
    .querySelector('.el-anchor__link[href="#basicInfo"]')
    .classList.add("is-active");
  document.querySelector(".el-anchor__marker").style.opacity = "1";
  document.querySelector(".el-anchor__marker").style.top = "0px";
});
onBeforeUnmount(() => {
  eventBus.off("fixedAnchorPoint");
});
</script>
<style lang="scss" scoped>
.detail-box {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  padding-bottom: 0;
  border-radius: 12px;
  position: relative;
  .detail-type {
    font-size: 16px;
    color: var(--default-color);
    font-weight: bold;
    line-height: 30px;
    margin-bottom: 16px;
    text-align: start;
  }
  .detail-item {
    text-align: start;
    margin-bottom: 20px;
    .detail-title {
      color: #777777;
      line-height: 21px;
      margin-bottom: 4px;
    }
    .detail-value {
      color: #000;
      line-height: 21px;
      width: 100%;
    }
  }
  .edit-box {
    position: absolute;
    top: 24px;
    right: 24px;
  }
}
.detail-box:last-child {
  margin-bottom: 0;
}
.el-anchor {
  transition: all 1s linear;
}
.fixed-anchor {
  position: fixed;
  top: 81px;
}
</style>
