/**
 * CSRF Token 刷新策略配置
 */

export interface CSRFRefreshConfig {
  // 刷新策略类型
  strategy: 'fixed' | 'smart' | 'visibility' | 'hybrid';
  
  // 固定间隔刷新配置
  fixedInterval?: number; // 毫秒
  
  // 智能刷新配置
  smartRefresh?: {
    checkInterval: number; // 检查间隔（毫秒）
    refreshBeforeExpiry: number; // 过期前多久刷新（毫秒）
  };
  
  // 页面可见性刷新配置
  visibilityRefresh?: {
    refreshAfterHidden: number; // 页面隐藏多久后需要刷新（毫秒）
  };
}

// 默认配置
export const defaultCSRFConfig: CSRFRefreshConfig = {
  strategy: 'hybrid',
  fixedInterval: 5 * 60 * 1000, // 5分钟
  smartRefresh: {
    checkInterval: 60 * 1000, // 1分钟检查一次
    refreshBeforeExpiry: 2 * 60 * 1000, // 过期前2分钟刷新
  },
  visibilityRefresh: {
    refreshAfterHidden: 3 * 60 * 1000, // 隐藏3分钟后刷新
  },
};

// 预设配置
export const presetConfigs = {
  // 保守策略：频繁刷新，确保 token 始终有效
  conservative: {
    strategy: 'fixed' as const,
    fixedInterval: 2 * 60 * 1000, // 2分钟
  },
  
  // 平衡策略：结合多种刷新方式
  balanced: {
    strategy: 'hybrid' as const,
    fixedInterval: 5 * 60 * 1000, // 5分钟
    smartRefresh: {
      checkInterval: 60 * 1000,
      refreshBeforeExpiry: 2 * 60 * 1000,
    },
    visibilityRefresh: {
      refreshAfterHidden: 3 * 60 * 1000,
    },
  },
  
  // 节约策略：减少不必要的请求
  economical: {
    strategy: 'smart' as const,
    smartRefresh: {
      checkInterval: 2 * 60 * 1000, // 2分钟检查一次
      refreshBeforeExpiry: 1 * 60 * 1000, // 过期前1分钟刷新
    },
  },
  
  // 仅在需要时刷新（适合低频使用场景）
  onDemand: {
    strategy: 'visibility' as const,
    visibilityRefresh: {
      refreshAfterHidden: 5 * 60 * 1000, // 隐藏5分钟后刷新
    },
  },
};

/**
 * CSRF Token 刷新管理器
 */
export class CSRFRefreshManager {
  private config: CSRFRefreshConfig;
  private timers: number[] = [];
  private refreshCallback: (() => Promise<void>) | null = null;
  
  constructor(config: CSRFRefreshConfig = defaultCSRFConfig) {
    this.config = config;
  }
  
  public setRefreshCallback(callback: () => Promise<void>): void {
    this.refreshCallback = callback;
  }
  
  public updateConfig(config: Partial<CSRFRefreshConfig>): void {
    this.config = { ...this.config, ...config };
    this.restart();
  }
  
  public start(): void {
    this.stop();
    
    switch (this.config.strategy) {
      case 'fixed':
        this.startFixedRefresh();
        break;
      case 'smart':
        this.startSmartRefresh();
        break;
      case 'visibility':
        this.startVisibilityRefresh();
        break;
      case 'hybrid':
        this.startHybridRefresh();
        break;
    }
  }
  
  public stop(): void {
    this.timers.forEach(timer => clearInterval(timer));
    this.timers = [];
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
  }
  
  public restart(): void {
    this.stop();
    this.start();
  }
  
  private startFixedRefresh(): void {
    if (this.config.fixedInterval) {
      const timer = window.setInterval(async () => {
        await this.executeRefresh();
      }, this.config.fixedInterval);
      this.timers.push(timer);
    }
  }
  
  private startSmartRefresh(): void {
    if (this.config.smartRefresh) {
      const timer = window.setInterval(async () => {
        await this.checkAndSmartRefresh();
      }, this.config.smartRefresh.checkInterval);
      this.timers.push(timer);
    }
  }
  
  private startVisibilityRefresh(): void {
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  }
  
  private startHybridRefresh(): void {
    this.startFixedRefresh();
    this.startSmartRefresh();
    this.startVisibilityRefresh();
  }
  
  private handleVisibilityChange = async (): Promise<void> => {
    if (!document.hidden && this.config.visibilityRefresh) {
      // 页面重新可见，检查是否需要刷新
      const shouldRefresh = this.shouldRefreshAfterVisibility();
      if (shouldRefresh) {
        await this.executeRefresh();
      }
    }
  };
  
  private shouldRefreshAfterVisibility(): boolean {
    // 这里可以根据实际需求实现检查逻辑
    // 例如检查上次刷新时间、token 过期时间等
    return true;
  }
  
  private async checkAndSmartRefresh(): Promise<void> {
    if (!this.config.smartRefresh) return;
    
    // 这里需要访问 CSRFTokenManager 来检查过期时间
    // 由于循环依赖问题，这个逻辑应该在使用时注入
    if (this.refreshCallback) {
      await this.refreshCallback();
    }
  }
  
  private async executeRefresh(): Promise<void> {
    try {
      if (this.refreshCallback) {
        await this.refreshCallback();
      }
    } catch (error) {
      console.warn('CSRF token refresh failed:', error);
    }
  }
}

// 导出单例实例
export const csrfRefreshManager = new CSRFRefreshManager();
