<template>
  <div class="page-statistical">
    <div class="page-title-section">
      <div class="page-title">问题统计看板</div>
      <div class="tools-bar">
        <el-select
          v-model="appTypeListValues"
          multiple
          clearable
          collapse-tags
          :max-collapse-tags="1"
          placeholder="请选择应用层级"
          popper-class="custom-header"
          class="select-app-type"
        >
          <template #header>
            <el-checkbox
              v-model="checkAll"
              :indeterminate="indeterminate"
              @change="handleCheckAll"
            >
              全选
            </el-checkbox>
          </template>
          <el-option
            v-for="item in appLayerList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>

        <el-input
          class="input-app-name"
          v-model="filterOptions.appName"
          style="max-width: 600px"
          placeholder="请输入应用"
          clearable
          @change="getChartData"
        >
          <template #append>
            <el-button :icon="Search" @click="getChartData" />
          </template>
        </el-input>

        <el-date-picker
          class="date-range"
          v-model="filterOptions.timeSegment"
          type="daterange"
          range-separator="到"
          start-placeholder="开始时间"
          :disabledDate="disabledDate"
          end-placeholder="结束时间"
          clearable
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="getChartData"
        />
        <el-radio-group
          class="radio-dateUnit"
          v-model="filterOptions.type"
          fill="rgba(255,255,255)"
          text-color="#333"
          @change="getChartData"
        >
          <el-radio-button label="日" value="day" />
          <el-radio-button label="周" value="week" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
      </div>
    </div>

    <el-card class="card-section">
      <template #header>
        <div class="card-header">
          <el-icon><PieChart /></el-icon>
          <span>问题解决进展分析</span>
        </div>
      </template>
      <div class="chart-body">
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="problemClosureData.show"
            :sourceData="problemClosureData"
            :useKey="problemClosureData.key"
            :title="problemClosureData.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="questionDistributionData.show"
            :sourceData="questionDistributionData"
            :useKey="questionDistributionData.key"
            :title="questionDistributionData.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="planOnlineTimeDistributionData.show"
            :sourceData="planOnlineTimeDistributionData"
            :useKey="planOnlineTimeDistributionData.key"
            :title="planOnlineTimeDistributionData.chartTitle"
          ></sourcesVolumeChart>
        </div>
      </div>
    </el-card>
    <el-card class="card-section" v-if="nextVersion">
      <template #header>
        <div class="card-header">
          <el-icon><DataLine /></el-icon>
          <span>源声转化问题单分析</span>
        </div>
      </template>
      <div class="chart-body">
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="allSourceData.show"
            :sourceData="allSourceData"
            :useKey="allSourceData.key"
            :title="allSourceData.chartTitle"
            @updateSelectVal="updateSelectVal"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="sourceSoundAnalysisCompletionRate.show"
            :sourceData="sourceSoundAnalysisCompletionRate"
            :useKey="sourceSoundAnalysisCompletionRate.key"
            :title="sourceSoundAnalysisCompletionRate.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="sourceSoundTransformationIssueSheet.show"
            :sourceData="sourceSoundTransformationIssueSheet"
            :useKey="sourceSoundTransformationIssueSheet.key"
            :title="sourceSoundTransformationIssueSheet.chartTitle"
          ></sourcesVolumeChart>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from "vue";
import type { CheckboxValueType } from "element-plus";
import { Search, DataLine, PieChart, Refresh } from "@element-plus/icons-vue";
import {
  appLayerList,
  problemSource,
} from "../../components/issuesOpinionManage/commonArea/checkList";
import sourcesVolumeChart from "./sourcesVolumeChart.vue";
import {
  getOriginChartData,
  queryTotalVolume,
  queryCompletionRate,
  queryProblemCount,
} from "./chartApi";
import { statusMap } from "./index";
const getAreaStyle = (color1, color2) => {
  return {
    opacity: 0.8,
    color: {
      type: "linear",
      x: 1,
      y: -3,
      x2: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: color1, // 0% 处的颜色
        },
        {
          offset: 1,
          color: color2, // 100% 处的颜色
        },
      ],
      global: false, // 缺省为 false}
    },
  };
};

// 全渠道声量趋势
let allSourceData = ref({});
// 问题类型分布
let questionDistributionData = ref({});

// 问题闭环处理趋势
let problemClosureData = ref({});
const nextVersion = ref(false); // 下个版本上

// 计划上线时间分布
const planOnlineTimeDistributionData = ref({});

// 源声分析完成率
let sourceSoundAnalysisCompletionRate = ref({});

// 源声转化问题单分析
const sourceSoundTransformationIssueSheet = ref({});

const checkAll = ref(false);
const indeterminate = ref(false);
const appTypeListValues = ref<CheckboxValueType[]>([]);
watch(appTypeListValues, (val) => {
  if (val.length === 0) {
    checkAll.value = false;
    indeterminate.value = false;
  } else if (val.length === appLayerList.length) {
    checkAll.value = true;
    indeterminate.value = false;
  } else {
    indeterminate.value = true;
  }
  filterOptions.value.appPriority = appTypeListValues;
  getChartData();
});

const handleCheckAll = (val: CheckboxValueType) => {
  indeterminate.value = false;
  if (val) {
    appTypeListValues.value = appLayerList.map((_) => _.value);
  } else {
    appTypeListValues.value = [];
  }
};

const updateSelectVal = (val) => {
  filterOptions.value.sourceSelect = val;
  getChartData();
};

const filterOptions = ref({
  appPriority: [],
  appName: "",
  timeSegment: [],
  type: "day",
  sourceSelect: [],
});

const setAllSourceData = (totalVolumeMap) => {
  const xAxisData = Object.keys(totalVolumeMap);
  const seriesData = Object.values(totalVolumeMap);
  allSourceData.value = {
    show: true,
    chartTitle: "全渠道声量趋势",
    id: "allSource",
    key: new Date().getTime(),
    legend: {
      bottom: 0,
      left: "center",
      icon: "circle",
      textStyle: {
        fontSize: "11px",
      },
      itemWdith: 12,
      itemHeight: 12,
    },
    select: {
      name: "sourceSelect",
      selectOptions: problemSource,
    },
    title: {
      text: "",
    },
    tooltip: {
      textStyle: {
        fontSize: "11px",
      },
    },
    xAxis: {
      type: "category",
      data: xAxisData,
    },
    yAxis: {},
    series: [
      {
        name: "声量趋势",
        type: "line",
        label: {
          show: true,
        },
        data: seriesData,
      },
    ],
  };
};

const setProblemClosureData = (xAxisData, series) => {
  problemClosureData.value = {
    key: new Date().getTime(),
    show: false,
    chartTitle: "问题闭环处理趋势",
    id: "problemClosure",
    legend: {
      bottom: 0,
      left: "center",
      icon: "circle",
      itemWdith: 12,
      itemHeight: 12,
    },
    title: {
      text: "",
    },
    tooltip: {
      textStyle: {
        fontSize: "11px",
      },
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    xAxis: {
      type: "category",
      data: xAxisData,
    },
    yAxis: {},
    series: [
      {
        name: "问题识别",
        tag: "0000",
        stack: "Total",
        smooth: true,
        type: "line",
        color: "rgb(32, 112, 243)",
        areaStyle: {
          ...getAreaStyle("blue", "rgba(32, 112, 243, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["问题识别"]],
        // data: [13887, 15620, 26820, 40779, 50142, 54709, 55284, 55930],
      },
      {
        name: "问题定界",
        tag: "0001",
        stack: "Total",
        smooth: true,
        type: "line",
        color: "rgba(98, 180, 46, 1)",
        areaStyle: {
          ...getAreaStyle("rgba(98, 180, 46, 1)", "rgba(98, 180, 46, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["问题定界"]],
      },
      {
        name: "计划锁定",
        tag: "0002",
        stack: "Total",
        smooth: true,
        type: "line",
        color: "#6D5BF2",
        areaStyle: {
          ...getAreaStyle("#6D5BF2", "rgba(109, 91, 242, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["计划锁定"]],
      },
      {
        name: "问题修复",
        smooth: true,
        tag: "0003",
        stack: "Total",
        type: "line",
        areaStyle: {
          ...getAreaStyle("rgba(44, 184, 201, 1)", "rgba(44, 184, 201, 0.1)"),
        },
        color: "rgb(44, 184, 201)",
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["问题修复"]],
      },
      {
        name: "验证闭环",
        type: "line",
        stack: "Total",
        tag: "0004",
        smooth: true,
        areaStyle: {
          ...getAreaStyle("rgba(246, 158, 57, 1)", "rgba(246, 158, 57, 0.1)"),
        },
        color: "rgba(246, 158, 57, 1)",
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["验证闭环"]],
      },
      {
        name: "关闭",
        type: "line",
        stack: "Total",
        tag: "0005",
        smooth: true,
        color: "rgba(232, 64, 38, 1)",
        areaStyle: {
          ...getAreaStyle("#FCE3DF", "rgba(232, 64, 38, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["关闭"]],
      },
    ],
  };
  problemClosureData.value.show = true;
};
const disabledDate = (time) => {
  return time > new Date();
};
const setQuestionDistributionData = (faultCategoryRatio) => {
  questionDistributionData.value = {
    show: true,
    key: new Date().getTime(),
    chartTitle: "问题类型分布",
    id: "questionDistribution",
    title: {
      text: "",
    },
    tooltip: {
      textStyle: {
        fontSize: "11px",
      },
      trigger: "item",
      formatter: (params) => {
        // params 是一个数组，包含所有被触发 action 的数据点信息
        let res =
          params.marker +
          "<span style='display:inline-block;width:5px;'></span>";
        res +=
          params.name +
          "<span style='display:inline-block;width:10px;'></span>";
        res += params.value + "%"; // 假设这里的单位是固定的，例如“单位”
        res += "<br/>";
        return res;
      },
    },
    legend: {
      bottom: 0,
      left: "center",
      icon: "circle",
      itemWdith: 12,
      itemHeight: 12,
    },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["40%", "55%"],
        center: ["50%", "40%"],
        label: {
          formatter: (params) => {
            let res = `${params.data.name}\n${params.data.value}%`;
            return res;
          },
          align: "center",
          lineHeight: 17,
        },
        data: faultCategoryRatio,
      },
    ],
  };
};

const setQlanOnlineTimeDistributionData = (planTimeRatio) => {
  planOnlineTimeDistributionData.value = {
    show: true,
    chartTitle: "计划上线时间分布",
    key: new Date().getTime(),
    id: "planOnlineTimeDistribution",
    title: {
      text: "",
    },
    tooltip: {
      textStyle: {
        fontSize: "11px",
      },
      trigger: "item",
      formatter: (params) => {
        // params 是一个数组，包含所有被触发 action 的数据点信息
        let res =
          params.marker +
          "<span style='display:inline-block;width:5px;'></span>";
        res +=
          params.name +
          "<span style='display:inline-block;width:10px;'></span>";
        res += params.value + "%"; // 假设这里的单位是固定的，例如“单位”
        res += "<br/>";
        return res;
      },
    },
    legend: {
      itemWdith: 12,
      itemHeight: 12,
      bottom: 0,
      left: "center",
      icon: "circle",
    },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["40%", "55%"],
        center: ["50%", "40%"],
        label: {
          show: true,
          formatter: (params) => {
            let res = `${params.data.name}\n${params.data.value}%`;
            return res;
          },
          align: "center",
          lineHeight: 17,
        },
        data: planTimeRatio,
      },
    ],
  };
};

const setsourceSoundAnalysisCompletionRate = (data) => {
  const xAxisData = Object.keys(data);
  const rates = Object.values(data).map((item) => (item * 100).toFixed(2));
  sourceSoundAnalysisCompletionRate.value = {
    show: true,
    chartTitle: "源声分析完成率",
    key: new Date().getTime(),
    id: "sourceSoundAnalysisCompletion",
    title: {
      text: "",
    },
    legend: {
      itemWdith: 12,
      itemHeight: 12,
      bottom: 0,
      left: "center",
      icon: "circle",
    },
    tooltip: {
      textStyle: {
        fontSize: "11px",
      },
      formatter: (params) => {
        // params 是一个数组，包含所有被触发 action 的数据点信息
        let res = params.seriesName + "<br/>"; // 显示数据点的名称
        res += params.marker;
        res +=
          params.name +
          "<span style='display:inline-block;width:10px;'></span>";
        res += params.value + "%"; // 假设这里的单位是固定的，例如“单位”
        res += "<br/>";
        return res;
      },
    },
    xAxis: {
      type: "category",
      data: xAxisData,
    },
    yAxis: {
      axisLabel: {
        formatter: "{value}%",
      },
    },
    series: [
      {
        name: "完成率",
        type: "line",
        label: {
          show: true,
          position: "top",
          textStyle: {
            fontSize: 12,
          },
          formatter: (params) => {
            return `${params.value}%`;
          },
        },
        data: rates,
      },
    ],
  };
};
const setsourceSoundTransformationIssueSheet = (data) => {
  const dates = Object.keys(data);
  const result = [];
  dates.forEach((dateItem) => {
    result.push({
      date: dateItem,
      源声个数: data[dateItem]["origin_count"],
      问题个数: data[dateItem]["issue_count"],
    });
  });
  sourceSoundTransformationIssueSheet.value = {
    show: true,
    chartTitle: "源声转化问题单分析",
    key: new Date().getTime(),
    id: "sourceSoundTransformationIssue",
    tooltip: {
      textStyle: {
        fontSize: "11px",
      },
    },
    legend: {
      itemWdith: 12,
      itemHeight: 12,
      bottom: 0,
      left: "center",
      icon: "circle",
    },
    dataset: {
      dimensions: ["date", "源声个数", "问题个数"],
      source: result,
    },
    xAxis: { type: "category" },
    yAxis: [
      {
        type: "value",
        name: "",
      },
      {
        type: "value",
        name: "",
      },
    ],
    series: [{ type: "bar" }, { type: "bar", yAxisIndex: 1 }],
  };
};

const getChartData = async () => {
  const timeSegment = filterOptions.value.timeSegment;
  const data = {
    appPriority: filterOptions.value.appPriority?.join(",") || "",
    appName: filterOptions.value.appName?.trim(),
    startTime: timeSegment ? timeSegment[0] : "",
    endTime: timeSegment ? timeSegment[1] : "",
    type: filterOptions.value.type,
    source: filterOptions.value.sourceSelect["value"]?.join(",") || "",
  };
  console.log(`获取图标数据 参数 `, JSON.stringify(data));
  try {
    const result = await getOriginChartData(data);
    // 问题闭环处理趋势数据
    const dailyStatus = result.dailyStatus;
    const xAxisData = Object.keys(dailyStatus);
    const series = {
      "0000": [],
      "0001": [],
      "0002": [],
      "0003": [],
      "0004": [],
      "0005": [],
    };
    const list = Object.keys(series);
    list.forEach((key) => {
      Object.keys(dailyStatus).forEach((item) => {
        Object.keys(dailyStatus[item]).forEach((list) => {
          if (key === list) {
            series[key].push(dailyStatus[item][list]);
          }
        });
      });
    });
    setProblemClosureData(xAxisData, series);
    // 问题类型分布
    let faultCategoryRatio = [{ name: "", value: "" }];
    faultCategoryRatio = Object.keys(result.faultCategoryRatio).map((key) => ({
      name: key,
      value: (result.faultCategoryRatio[key] * 100).toFixed(2),
    }));
    let planTimeRatio = [{ name: "", value: "" }];
    planTimeRatio = Object.keys(result.planTimeRatio).map((key) => ({
      name: key,
      value: (result.planTimeRatio[key].toString() * 100).toFixed(2),
    }));
    setQuestionDistributionData(faultCategoryRatio);
    setQlanOnlineTimeDistributionData(planTimeRatio);
  } catch (e) {
    console.error("getChartData 报错", e);
  }

  try {
    const result = await queryTotalVolume(data);
    setAllSourceData(result);
  } catch (e) {
    console.error("getChartData 报错", e);
  }
  try {
    const result = await queryCompletionRate(data);

    setsourceSoundAnalysisCompletionRate(result);
  } catch (e) {
    console.error("getChartData 报错", e);
  }
  try {
    const result = await queryProblemCount(data);

    setsourceSoundTransformationIssueSheet(result);
  } catch (e) {
    console.error("getChartData 报错", e);
  }
};

getChartData();
</script>

<style lang="less" scoped>
.tools-bar {
  font-size: 14px;
  display: flex;
  .select-app-type,
  .date-range,
  .input-app-name {
    width: 200px;
  }
  > * {
    margin-left: 15px;
    height: 32px;
  }
}
.chart-body {
  display: flex;
  background-color: rgb(245, 245, 245);
  .chart-section {
    background-color: #fff;
    flex: 1;
    height: 348px;
    border-radius: 8px;
    margin: 12px;
    padding: 20px;
  }
  > :first-child {
    flex: 1;
  }
}
</style>

<style>
.page-statistical .tools-bar {
  .date-range {
    width: 260px;
  }
  .radio-dateUnit {
    .el-radio-button__inner {
      background: rgba(255, 255, 255, 0.01);
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
