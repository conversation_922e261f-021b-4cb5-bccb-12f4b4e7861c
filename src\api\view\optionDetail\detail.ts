import { ewpService as service } from '@/utils/axios';

export async function fetchKeyInfomation(id: any): Promise<any> {
    try {
        const response = await service.get(`/ewpPro/ewpOrder/delimitView?id=${id} `);
        return response;
    } catch (error) {
        throw error;
    }
}

export async function fetchProgressData(params: any): Promise<any> {
    try {
        const response = await service.post('/progressInfo/query', params, {
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function fetchStatus(id): Promise<any> {
    try {
        const response = await service.get(`/opinionIssue/queryStatus?opinionIssueId=${id}`);
        return response;
    } catch (error) {
        throw error;
    }
}

export async function fetchMaxStepBeforeBack(id): Promise<any> {
    try {
        const response = await service.get(`/progressInfo/queryMaxStepBeforeBack?opinionIssueId=${id}`);
        return response;
    } catch (error) {
        throw error;
    }
}


export async function fetchStepPerson(id): Promise<any> {
    try {
        const response = await service.get(`/progressInfo/queryStepPerson?opinionIssueId=${id}`);
        return response;
    } catch (error) {
        throw error;
    }
}

export async function fetchDetails(params: any): Promise<any> {
    try {
        const response = await service.post('/progressInfo/detail', params, {
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function fetchIssueDetail(id: any): Promise<any> {
    try {
        const response = await service.get(`/opinionIssue/queryStatus?opinionIssueId=${id}`);
        return response;
    } catch (error) {
        throw error;
    }
}

/**
 * 编辑基本信息
 */
export const opinionIssueUpdate = async (params) => {
    try {
        const response = await service.post(`/opinionIssue/update`, params);
        return response;
    } catch (error) {
        throw error;
    }
}