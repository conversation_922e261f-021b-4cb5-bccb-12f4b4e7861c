import { ewpService as service } from '@/utils/axios';

export const fetchLockSolutionTable = async (opinionIssueId) => {
  try {
    const progressedResponse = {
      lockPlan: {},
      relateWorkOrders: [],
    }
    const response = await service.get(`/lockResolvePlan/view?opinionIssueId=${opinionIssueId}`, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    response.relateWorkOrders.forEach(item => {
      const row = {}
      row.orderType = item.relatedOrderType
      row.order = item.relatedOrderId
      row.progress = item.issueProgress
      row.plannedResolutionTime = item.planTime?.slice(0, 10).replaceAll('/', '-')
      row.sort = item.sort
      progressedResponse.relateWorkOrders.push(row)
    });
    progressedResponse.lockPlan = {
      plannedResolutionTime: response.lockPlan?.planTime,
      progress: response.lockPlan?.issueProgress
    }
    return progressedResponse
  } catch (error) {
      throw error;
  }
}

// 保存解决计划
export const saveLockSolution = async ( params ) => {
  try {
    const response = await service.post('/lockResolvePlan/save', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response;
  } catch (error) {
      throw error;
  }
}

// 完成/转单
export const submitLockSolution = async (params) => {
  try {
    const response = await service.post('/lockResolvePlan/submit', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response;
  } catch (error) {
      throw error;
  }
}