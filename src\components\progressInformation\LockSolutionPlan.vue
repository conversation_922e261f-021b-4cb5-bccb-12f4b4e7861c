<template>
  <div class="form-card">
    <div class="form-title">锁定解决计划</div>
    <div class="description">进展信息</div>
    <el-table class="table" :data="tableDataShow" style="width: 100%">
      <el-table-column prop="sort" :label="'序号'" width="88" />
      <el-table-column
        v-for="item in tableColumns"
        :key="item.key"
        :prop="item.key"
        :label="item.label"
      >
        <template #header>
          <span><span class="required-field">*</span>{{ item.label }}</span>
        </template>
        <template #default="scope">
          <el-select
            v-if="item.type === 'select'"
            v-model="scope.row[item.key]"
            placeholder="请选择内容"
            :disabled="!canEdit"
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-input
            v-if="item.type === 'input'"
            :placeholder="item.placeholder || '请输入内容'"
            v-model="scope.row[item.key]"
            :disabled="!canEdit"
            maxlength="250"
          />
          <el-date-picker
            v-if="item.type === 'datePicker'"
            v-model="scope.row[item.key]"
            placeholder="请选择时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="changeDate"
            :disabled="!canEdit"
            class=""
          />
        </template>
      </el-table-column>
      <el-table-column v-if="canEdit" fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-icon
            :class="{ disabled: tableDataShow.length < 2 || !canEdit }"
            :disabled="tableDataShow.length < 2 || !canEdit"
            @click="deleteRow(scope)"
            ><Delete
          /></el-icon>
          <el-icon
            v-if="scope.$index === tableDataShow.length - 1"
            :class="{ disabled: !canEdit }"
            style="margin-left: 16px; color: rgb(10, 89, 247)"
            @click="addRow(scope)"
            ><Plus
          /></el-icon>
        </template>
      </el-table-column>
    </el-table>
    <el-form
      :model="formData"
      label-width="auto"
      :rules="formRules"
      ref="formRef"
      :disabled="!canEdit"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item
            label="问题进展"
            :label-position="'top'"
            class="form-item"
            prop="progress"
          >
            <template #label>
              <span> 问题进展 </span>
              <span v-if="canEdit" id="summary" @click="onSummary">
                一键提取结论
              </span>
            </template>
            <el-input
              placeholder="【问题根因】xxxxxx
【解决方案】xxxxxx"
              v-model="formData.progress"
              :row="2"
              type="textarea"
              show-word-limit
              maxlength="2000"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="计划解决时间"
            :label-position="'top'"
            class="form-item"
            prop="plannedResolutionTime"
          >
            <el-date-picker
              v-model="formData.plannedResolutionTime"
              aria-label="Pick a date"
              placeholder="请选择时间"
              style="width: 368px"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="description" v-if="!isNodeFinished && canEdit">操作信息</div>
    <el-form
      ref="formRef"
      :inline="true"
      :model="assignmentFormData"
      :rules="rules"
      label-width="95"
      label-position="top"
      v-if="!isNodeFinished && canEdit"
    >
      <el-form-item label="操作类型" prop="operationType">
        <el-radio-group
          v-model="assignmentFormData.operationType"
          @change="changeOperationType"
        >
          <el-radio :value="operationOpt[1].value">{{
            operationOpt[1].label
          }}</el-radio>
          <el-radio :value="operationOpt[0].value">{{
            operationOpt[0].label
          }}</el-radio>
          <el-radio :value="operationOpt[2].value">{{
            operationOpt[2].label
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="指定处理人" prop="handler" v-if="assignmentFormData.operationType !== '0002'">
        <el-select
          placeholder="请选择内容"
          filterable
          v-model="assignmentFormData.handler"
        >
          <el-option
            v-for="item in handleOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="canEdit" class="form-button text-center">
      <el-button class="big-button" @click="onSave">{{
        isNodeFinished ? "更新信息" : "保存"
      }}</el-button>
      <el-button
        v-if="!isNodeFinished"
        class="big-button"
        type="primary"
        @click="assignmentSubmit"
        >提交</el-button
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, computed, onMounted, toRefs } from "vue";
import type { FormInstance } from "element-plus";
import { ElLoading, ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import { Delete, Plus } from "@element-plus/icons-vue";

import {
  fetchLockSolutionTable,
  saveLockSolution,
  submitLockSolution,
} from "@/business/progressInformation/lockSolutionPlan";
import { operationTypeOptions as operationOpt } from "@/business/progressInformation/delimitProblem";
import { orderTypeOptions } from "@/business/progressInformation/relateOrder";
import { getHandlers } from "@/business/progressInformation/common/getHandlers";
import { cloneDeep, isEqual } from "lodash-es";
import {getValidateFun, validateTable} from "@/business/progressInformation/common/validateUtil";

const formRules = reactive({
  plannedResolutionTime: [
    { required: true, message: "请输入计划解决时间", trigger: "blur" },
  ],
  progress: [
    { required: true, message: "请输入问题进展", trigger: "blur" },
    { validator: getValidateFun("问题进展"), trigger: 'blur' }
  ],
});

const emit = defineEmits(["refresh"]);

const validateOrder = (oneRow) => {
  const { orderType, order } = oneRow;
  if (orderType === "DTS") {
    return /^DTS\d{13}$/.test(order);
  } else if (orderType === "IR") {
    return /^\d{15}$/.test(order);
  } else if (orderType === "TALM") {
    return /^RR-\d{8}$/.test(order);
  } else if (orderType === "IONE") {
    return /^SR\d{14}$/.test(order);
  }
};

const refreshParentData = (step = null) => {
  emit("refresh", step);
};

const props = defineProps({
  node: {
    default: () => {},
    required: true,
  },
  canEdit: {
    default: () => false,
    required: true,
  },
});

const { node } = toRefs(props);
const isNodeFinished = node.value.isFinished;

const route = useRoute();
const opinionIssueId = route.query.id;
const formRef = ref<FormInstance>();
const tableColumns = [
  {
    label: "关联单类型",
    key: "orderType",
    type: "select",
    options: orderTypeOptions,
  },
  {
    label: "关联单号",
    key: "order",
    type: "input",
  },
  {
    label: "计划解决进展",
    key: "progress",
    type: "input",
    placeholder: "【问题根因】xxxxxx，【解决方案】xxxxxx",
  },
  {
    label: "计划解决时间",
    key: "plannedResolutionTime",
    type: "datePicker",
  },
];

// 锁定解决计划
let formData = ref({
  progress: "",
  plannedResolutionTime: "",
  index: "",
});
const rules = {
  // 页面表单
  progress: [
      { required: true, message: "问题进展必填！", trigger: "blur" },
    { validator: getValidateFun("问题进展"), trigger: 'blur' }
  ],
  plannedResolutionTime: [
    { required: true, message: "计划解决时间必填！", trigger: "blur" },
    { validator: getValidateFun("解决时间"), trigger: 'blur' }
  ],
  // 指派
  operationType: [
    { required: true, message: "操作类型必填！", trigger: "blur" },
  ],
  handler: [{ required: true, message: "指定处理人必填！", trigger: "blur" }],
};

// 修改表格时间同步最晚时间
const changeDate = () => {
  let latestDateText = "";
  let latestDate = null;
  tableDataShow.value.forEach((item) => {
    if (!latestDate) {
      latestDateText = item.plannedResolutionTime;
      latestDate = new Date(item.plannedResolutionTime);
    } else if (new Date(item.plannedResolutionTime) > latestDate) {
      latestDateText = item.plannedResolutionTime;
      latestDate = item.plannedResolutionTime;
    }
  });
  formData.value.plannedResolutionTime = latestDateText;
};

// 校验表单
const checkProgress = () => {
  return formData.value.progress && formData.value.plannedResolutionTime && validateTable(formData.value.progress);
};

// 校验表格是否填写完整
const checkTableDataRequired = () => {
  const hasEmptyRow = tableDataShow.value.some((item) => {
    return (
      !item.orderType ||
      !item.order ||
      !item.progress ||
      !item.plannedResolutionTime ||
      !validateTable(item.orderType) ||
      !validateTable(item.order) ||
      !validateTable(item.progress) ||
      !validateTable(item.plannedResolutionTime)
    );
  });
  return !hasEmptyRow;
};

// 校验操作信息
const checkOperation = () => {
  if (!assignmentFormData.handler && !assignmentFormData.operationType) {
    return false;
  }
  return true;
};

// 检查表格填写是否正确
const checkTableDataValid = () => {
  const notValid = tableDataShow.value.some((item) => {
    return !validateOrder(item);
  });
  return !notValid;
};

// 校验参数
const checkParams = (needOperator) => {
  const isProgressValid = checkProgress();
  if (!isProgressValid) {
    ElMessage({
      type: "error",
      message: "请填计划解决时间和问题进展",
    });
    return false;
  }
  const isTableFinished = checkTableDataRequired();
  if (!isTableFinished) {
    ElMessage({
      type: "error",
      message: "请完整填写表格信息",
    });
    return false;
  }
  const isTableValid = checkTableDataValid();
  if (!isTableValid) {
    ElMessage({
      type: "error",
      message: "请填写正确的单号",
    });
    return false;
  }
  if (needOperator) {
    const isOperationValid = checkOperation();
    if (!isOperationValid) {
      ElMessage({
        type: "error",
        message: "请填写操作类型和处理人",
      });
      return false;
    }
  }
  return true;
};

const getParams = () => {
  return {
    opinionIssueId,
    planTime: formData.value.plannedResolutionTime?.slice(0, 10),
    nextHandler: assignmentFormData.handler,
    operationType: assignmentFormData.operationType,
    issueProgress: formData.value.progress,
    relateWorkOrders: tableDataShow.value.map((item) => {
      return {
        relatedOrderType: item.orderType,
        relatedOrderId: item.order,
        sort: item.sort,
        issueProgress: item.progress,
        planTime: item.plannedResolutionTime,
      };
    }),
  };
};

// 一键提取结论
const onSummary = () => {
  const currentProgress = formData.value.progress;
  let addText = currentProgress ? "\n" : "";
  tableDataShow.value.forEach((item, index) => {
    addText += `${index > 0 ? "\n" : ""}${item.order}：${item.progress || ""}`;
  });
  formData.value.progress = (currentProgress || "") + addText;
};

// 保存
const onSave = async () => {
  if (!checkParams(false)) {
    return;
  }
  if (
    isNodeFinished &&
    isEqual(
      { ...formData.value, solutionTable: solutionTable.value },
      originData.value
    )
  ) {
    ElMessage({
      type: "error",
      message: "内容未修改,请修改内容后更新",
    });
    return;
  }
  try {
    const params = getParams();
    (await isNodeFinished)
      ? submitLockSolution(params)
      : saveLockSolution(params);
    ElMessage({
      type: "success",
      message: isNodeFinished ? "更新成功" : "保存成功",
    });
    refreshParentData(node.value.step);
  } catch (err) {
    ElMessage({
      type: "error",
      message: isNodeFinished ? "更新失败" : "保存失败",
    });
  }
};

// 提交状态
const showAssignment = ref(false);
const handleOptions = ref([]);
const assignmentFormData = reactive({
  operationType: "",
  handler: "",
});

const deleteRow = (scope) => {
  if (tableDataShow.value.length < 2) {
    return;
  }
  solutionTable.value.splice(scope.$index, 1);
};

const addRow = (scope) => {
  solutionTable.value.push({
    sort: Number(tableDataShow.value[tableDataShow.value.length - 1].sort) + 1, // 取最后一条数据的 sort 加1
    ...defaultTableRow,
  });
};

const assignmentSubmit = async () => {
  const needCheckParams =
    !assignmentFormData.operationType ||
    assignmentFormData.operationType === operationOpt[1].value;
  if (needCheckParams && !checkParams(true)) {
    return;
  }
  try {
    const params = getParams();
    await submitLockSolution(params);
    // 关闭对话框
    showAssignment.value = false;

    refreshParentData();
    // 显示成功提示
    ElMessage({
      type: "success",
      message: "提交成功",
    });
  } catch (err) {
    console.error("提交失败:");
    ElMessage({
      type: "error",
      message: "提交失败",
    });
  }
};

// 用户修改操作类型
const changeOperationType = async (operationType) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let handlerList = null;
  if (opinionIssueId) {
    handlerList = await getHandlers(opinionIssueId, operationType);
  }
  if (handlerList) {
    handleOptions.value = handlerList.handlers;
    assignmentFormData.handler = handlerList.defaultHandler;
  } else {
    ElMessage({
      type: "error",
      message: "获取下一步处理人失败",
    });
  }
  loading.close();
};

const originData = ref({});

// 初始化时获取复现设备数据
onMounted(async () => {
  if (opinionIssueId) {
    const response = await fetchLockSolutionTable(opinionIssueId);
    if (response.relateWorkOrders?.length === 0) {
      response.relateWorkOrders.push({
        sort: "1",
        orderType: "",
        order: "",
        progress: "",
        plannedResolutionTime: "",
      });
    }
    solutionTable.value = response.relateWorkOrders;
    formData.value = response.lockPlan;
    originData.value = cloneDeep({
      ...formData.value,
      solutionTable: solutionTable.value,
    });
  }
});

const solutionTable = ref([]);

// 新增一行提供默认值
const defaultTableRow = {
  orderType: "",
  order: "",
  progress: "",
  plannedResolutionTime: "",
};

// 添加索引
const tableDataShow = computed(() => {
  return solutionTable.value;
});
</script>
<style lang="scss" scoped>
.form-card {
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 24px;

  .form-title {
    color: rgb(25, 25, 25);
    font-family: 鸿蒙黑体;
    font-size: 16px;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: 0px;
    text-align: left;
  }

  .description {
    margin-top: 20px;
    background: rgb(245, 245, 245);
    color: rgb(25, 25, 25);
    font-family: 鸿蒙黑体;
    font-size: 14px;
    font-weight: 700;
    line-height: 40px;
    letter-spacing: 0px;
    text-align: left;
    height: 40px;
    padding-left: 12px;
  }

  .el-form {
    margin-top: 24px;
    #summary {
      color: rgb(10, 89, 247);
      cursor: pointer;
      display: inline-block;
      margin-left: 4px;
      font-size: 12px;
    }
  }

  .el-table {
    border: 1px solid rgba(17, 26, 44, 0.1);
    border-radius: 8px;
    thead {
      th {
        background: rgb(248, 248, 248);
        /* 添加必填项红色星号样式 */
        .required-field {
          color: #f56c6c;
          margin-right: 4px;
        }
      }
    }
    .el-icon {
      cursor: pointer;
      &.disabled {
        cursor: not-allowed;
        color: gray;
      }
    }
    :deep(.el-date-editor) {
      width: 100%;
    }
  }

  .form-box {
    margin-top: 24px;
  }

  .form-button {
    margin-top: 16px;
    .el-button + .el-button {
      margin-left: 24px;
    }
  }
}
.table {
  margin-top: 16px;
}
.text-center {
  text-align: center;
}
</style>
