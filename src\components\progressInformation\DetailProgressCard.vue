<template>
  <div class="card detail-progress-card" style="margin-top: 24px">
    <div class="title">操作日志</div>
    <el-divider style="margin-bottom: 20px" />
    <el-table class="table" :data="detailTableData" style="width: 100%">
      <template #empty>
        <div class="empty-container">
          <el-icon :size="56"><FolderOpened /></el-icon>
          <div class="empty-text">暂无数据</div>
        </div>
      </template>
      <el-table-column
        :prop="'index'"
        type="index"
        :index="
          (index) => pagination.size * (pagination.current - 1) + index + 1
        "
        :label="'序号'"
        :width="'88px'"
      />
      <el-table-column
        v-for="item in detailTableColumns"
        :key="item.key"
        :prop="item.key"
        :label="item.label"
        :width="item.width"
      />
    </el-table>
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :page-sizes="pagination.pageSizes"
      :size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { FolderOpened } from "@element-plus/icons-vue";
import { detailTableColumns } from "@/business/progressInformation/detailProgress";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { fetchDetails } from "@/api/view/optionDetail/detail";
import dayjs from "dayjs";
import {
  statusList,
  stepList,
  transValue2Label,
} from "@/components/issuesOpinionManage/commonArea/checkList";
const route = useRoute();
const id = ref("0");

const tableModule = () => {
  const pagination = ref({
    current: 1,
    size: 10,
    pageSizes: [10, 20, 50, 100],
    total: 10,
  });
  const detailTableData = ref([]);
  const fetchTableData = async () => {
    const param = {
      pageNo: pagination.value.current,
      pageSize: pagination.value.size,
      opinionIssueId: id.value,
    };
    const resp = await fetchDetails(JSON.stringify(param));
    pagination.value.total = resp.total;
    detailTableData.value = resp.records.map((item) => {
      item.flowType = item.flowType === "0000" ? "流转" : "更新";
      item.createTime = dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss");
      item.currentStatus = transValue2Label(item.currentStatus, statusList);
      item.currentStep = transValue2Label(item.currentStep, stepList);
      return item;
    });
  };

  const handleSizeChange = (num) => {
    pagination.value.size = num;
    pagination.value.current = 1;
    fetchTableData();
  };

  const handleCurrentChange = (num) => {
    pagination.value.current = num;
    fetchTableData();
  };

  const refresh = () => {
    pagination.value.size = 10;
    pagination.value.current = 1;
    fetchTableData();
  };

  return {
    refresh,
    detailTableData,
    pagination,
    fetchTableData,
    handleSizeChange,
    handleCurrentChange,
  };
};

const {
  refresh,
  detailTableData,
  pagination,
  fetchTableData,
  handleSizeChange,
  handleCurrentChange,
} = tableModule();

onMounted(() => {
  id.value = route.query.id as string;
  fetchTableData();
});

defineExpose({ refresh });
</script>

<style lang="scss" scoped>
.detail-progress-card {
  .title {
    color: rgba(0, 0, 0, 0.9);
    font-family: 微软雅黑;
    font-size: 16px;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: 0%;
    text-align: left;
    padding: 5px 0;
  }

  .el-divider {
    margin-top: 0;
  }

  .table {
    margin-top: 16px;
    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .empty-text {
        color: rgb(25, 25, 25);
        font-family: 微软雅黑;
        font-size: 14px;
        font-weight: 700;
        line-height: 150%;
        letter-spacing: 0%;
        text-align: center;
        margin-top: 4px;
        margin-bottom: 12px;
      }
    }
  }

  .el-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    :deep(.el-pagination__total) {
      margin-right: auto;
    }
    :deep(.el-pagination__sizes) {
      order: 2;
    }
    :deep(.el-pagination__jump) {
      order: 3;
    }
  }
}
</style>
