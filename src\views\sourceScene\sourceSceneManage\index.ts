import {
  productMap,
  problemSource,
  columnList,
  appLayerList,
  addressList,
  classificationOptions,
  priorityOption,
} from "@/components/issuesOpinionManage/commonArea/checkList";
import { option } from "@/views/metaManager";

export const defaultFormData = {
  appName: "",
  packageName: "", // 应用包名
  appVersion: "",
  description: "", // 源声描述
  source: "",
  volume: "", // 源声声量
  productType: "",
  productModel: "",
  sceneName: "",
  systemVersion: "",
  createOpinionIssue: false,
};

export const defaultProductCheckAll = {
  productType: false,
  productModel: false,
};

export const defaultProductIndeterminate = {
  productType: false,
  productModel: false,
};

export const SELECT_ALL = 'ALL';

export const yesOrNoOption = [
  {
    label: "是",
    value: true,
  },
  {
    label: "否",
    value: false,
  },
];

export const sourceFormComponentsData = [
  {
    label: "应用名称",
    key: "appName",
    component: "Input",
    rules: { required: true, message: "应用必填！" },
  },
  { label: "应用包名", key: "packageName", component: "Input" },
  {
    label: "源声描述",
    key: "description",
    component: "Input",
    rules: { required: true, message: "源声描述必填！" },
  },
  {
    label: "源声声量",
    key: "volume",
    component: "Input",
    rules: {
      required: true,
      validator(rule, value, callback) {
        if (!value) {
          return new Error("源声声量必填！");
        }
        if (!/^[1-9]\d*$/.test(value)) {
          return new Error("输入有误，请检查！");
        }
        if (value <= 0 || value > 2147483647) {
          return new Error("请输入1~2147483647的值！");
        }
        return true;
      },
    },
  },
  {
    label: "问题来源",
    key: "source",
    component: "Select",
    multiple: true,
    option: problemSource,
  },
  {
    label: "应用版本",
    key: "appVersion",
    component: "Input",
  },
  {
    label: "场景名称",
    key: "sceneName",
    component: "Input",
    rules: { required: true, message: "场景名称必填！" },
  },
  {
    label: "系统版本",
    key: "osVersion",
    component: "Input",
  },
  {
    label: "问题发生时间",
    key: "reportedTime",
    component: "DatePicker",
    disabledDate: time => {
      return time > new Date()
    }
  },
  {
    label: "产品类型",
    key: "productType",
    component: "Select",
    option: Object.keys(productMap).map(item => { return { label: item, value: item } }),
    rules: { required: true, message: "产品类型必填！" },
    multiple: true,
  },
  {
    label: "产品机型",
    key: "productModel",
    component: "Select",
    multiple: true,
  },
  {
    label: "场景分类编码",
    key: "sceneId",
    component: "Input",
    placeholder: " ",
    disabled: true,
  },
  {
    label: "API版本",
    component: "Input",
    key: "apiVersion",
  },
  {
    rules: { required: true, message: "问题分类必填！" },
    label: "问题分类",
    key: "faultCategory",
    component: "Select",
    option: classificationOptions,
  },
  {
    label: "问题提出人",
    key: "reportedPerson",
    component: "Input",
    disabled: true,
  },
  {
    label: "建议解决时间",
    component: "DatePicker",
    key: "suggestResolveTime",
    disabledDate: time => {
      return time.getTime() < Date.now() - 86400000;// 解决时间禁止选择今天以前的时间
    }
  },
  {
    key: "remark",
    label: "备注",
    component: "Input",
  },
  {
    label: "创建问题",
    key: "createOpinionIssue",
    component: "Radio",
    defaultValue: "",
    disabled: false,
    option: yesOrNoOption,
  },
];

export const cPublicProblemFormComponentsData = [
  {
    label: "应用名称",
    key: "appName",
    component: "Input",
    rules: { required: true, message: "应用必填！" },
    disabled: true,
  },
  {
    label: "应用包名",
    key: "appPackageName",
    component: "Input",
  },
  {
    label: "场景分类编码",
    key: "sceneId",
    component: "Input",
    rules: { required: true, message: "场景分类编码必填！" },
    disabled: true,
  },
  {
    label: "聚类时间",
    key: "clusterTime",
    component: "DatePicker",
    rules: { required: true, message: "聚类时间必填！" },
  },
  {
    label: "优先级",
    key: "priority",
    component: "Select",
    option: priorityOption,
    rules: { required: true, message: "优先级必填！" },
  },
  {
    label: "问题来源",
    key: "channelSource",
    component: "Select",
    multiple: true,
    option: problemSource,
  },
  {
    label: "场景名称",
    key: "sceneName",
    component: "Input",
    rules: { required: true, message: "场景名称必填！" },
    disabled: true,
  },
  {
    label: "L3/6功能",
    key: "levelFunction",
    component: "Input",
  },
  {
    label: "责任团队/TL",
    key: "responsibleTeam",
    component: "Input",
  },
  {
    label: "责任人",
    key: "owner",
    component: "Input",
  },
  {
    label: "首次提单时间",
    key: "firstReportTime",
    component: "DatePicker",
  },
  {
    label: "汇总声量",
    key: "totalVolume",
    component: "Input",
    disabled: true,
  },
  {
    label: "产品类型",
    key: "productType",
    component: "Select",
    option: Object.keys(productMap).map((item) => {
      return { label: item, value: item };
    }),
    multiple: true,
  },
  {
    label: "产品机型",
    key: "productModel",
    component: "Select",
    multiple: true,
  },
  {
    label: "应用版本",
    key: "appVersion",
    component: "Input",
  },
  {
    label: "系统版本",
    key: "osVersion",
    component: "Input",
  },
  {
    label: "问题分类",
    key: "faultCategory",
    component: "Select",
    option: classificationOptions,
    rules: { required: true, message: "问题分类必填！" },
  },
  {
    label: "源声场景类型",
    key: "automated",
    component: "Input",
  },
  {
    label: "问题提出人",
    key: "reportedPerson",
    component: "Input",
    disabled: true,
  },
  {
    label: "建议解决时间",
    key: "suggestResolveTime",
    component: "DatePicker",
    disabledDate: time => {
      return time.getTime() < Date.now() - 86400000;// 解决时间禁止选择今天以前的时间
    }
  },
  {
    label: "备注",
    key: "remark",
    component: "Input",
  },
  {
    label: "指定处理人",
    key: "disposePerson",
    component: "Select",
    option: [],
    rules: { required: true, message: "指定处理人必填！" },
  },
];

export const allColumn = [
  { prop: "appName", label: "应用", minWidth: "" },
  { prop: "appPackageName", label: "应用包名", minWidth: "110" },
  { prop: "sceneId", label: "场景分类编码", minWidth: "150" },
  { prop: "sceneName", label: "场景名称", minWidth: "180" },
  { prop: "automated", label: "源声场景类型", minWidth: "120" }, // 1是自动，0是人工
  { prop: "channelSource", label: "问题来源", minWidth: "120" },
  {
    prop: "firstReportTime",
    label: "用户首次提单时间",
    minWidth: "150",
    time: true,
  },
  { prop: "clusterTime", label: "聚类时间", minWidth: "110", time: true },
  {
    prop: "totalVolume",
    label: "汇总声量",
    minWidth: "120",
    sortable: 'custom',
  },
  { prop: "productType", label: "产品类型", minWidth: "130" },
  { prop: "productModel", label: "产品机型", minWidth: "130" },
  { prop: "appVersion", label: "应用版本", minWidth: "100" },
  { prop: "osVersion", label: "系统版本", minWidth: "100" },
  { prop: "faultCategory", label: "问题分类", minWidth: "100" },
  { prop: "reportedPerson", label: "问题提出人", minWidth: "100" },
  {
    prop: "suggestResolveTime",
    label: "建议解决时间",
    minWidth: "120",
    time: true,
  },
  { prop: "remark", label: "备注", minWidth: "120" },
];

export const defaultColumn = [
  { prop: "appName", label: "应用", minWidth: "" },
  { prop: "sceneId", label: "场景分类编码", minWidth: "150" },
  { prop: "sceneName", label: "场景名称", minWidth: "180" },
  { prop: "automated", label: "源声场景类型", minWidth: "120" }, // 1是自动，0是人工
  { prop: "channelSource", label: "问题来源", minWidth: "120" },
  {
    prop: "firstReportTime",
    label: "用户首次提单时间",
    minWidth: "150",
    time: true,
  },
  { prop: "clusterTime", label: "聚类时间", minWidth: "110", time: true },
  {
    prop: "totalVolume",
    label: "汇总声量",
    minWidth: "120",
    sortable: "custom",
  },
  { prop: "productType", label: "产品类型", minWidth: "130" },
  { prop: "productModel", label: "产品机型", minWidth: "130" },
  { prop: "appVersion", label: "应用版本", minWidth: "100" },
  { prop: "osVersion", label: "系统版本", minWidth: "100" },
  { prop: "faultCategory", label: "问题分类", minWidth: "100" },
  { prop: "reportedPerson", label: "问题提出人", minWidth: "100" },
  {
    prop: "suggestResolveTime",
    label: "建议解决时间",
    minWidth: "120",
    time: true,
  },
  { prop: "remark", label: "备注", minWidth: "120" },
];

export const defaulList = [
  { label: "问题来源", key: "source", list: problemSource },
  { label: "纵队", key: "team", list: columnList },
  { label: "应用层级", key: "appPriority", list: appLayerList },
];
export const otherList = [
  { label: "归属代表处/系统部", key: "represent", list: addressList },
];
