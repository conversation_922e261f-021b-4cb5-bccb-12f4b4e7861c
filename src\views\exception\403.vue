<template>
  <div class="exception-container">
    <div class="exception-content">
      <img
        src="~@/assets/images/exception/403.svg"
        alt="403"
        class="exception-image"
      />
      <h2 class="exception-title">访问受限</h2>
      <p class="exception-desc">抱歉，您没有权限访问此页面</p>
      <div class="exception-actions">
        <el-button @click="applyPermission" :icon="Key"> 申请权限 </el-button>
      </div>
      <p class="exception-help">
        如需帮助，请参考
        <el-link
          type="primary"
          :underline="false"
          href="https://wiki.huawei.com/domains/62854/wiki/122722/WIKI202507237602848"
          target="_blank"
          >权限申请文档</el-link
        >
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from "vue-router";
import { HomeFilled, Key } from "@element-plus/icons-vue";

const router = useRouter();

function goHome() {
  router.push("/");
}

function applyPermission() {
  window.open(
    "https://wiki.huawei.com/domains/62854/wiki/122722/WIKI202507237602848",
    "_blank"
  );
}
</script>

<style lang="scss" scoped>
.exception-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.exception-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px;
  background-color: #fff;
  border-radius: 8px;
  width: 100%;
}

.exception-image {
  width: 240px;
  margin-bottom: 24px;
}

.exception-title {
  font-size: 24px;
  color: #303133;
  margin: 0 0 16px;
  font-weight: 500;
}

.exception-desc {
  font-size: 16px;
  color: #606266;
  margin: 0 0 24px;
  text-align: center;
}

.exception-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.exception-help {
  font-size: 14px;
  color: #909399;
  text-align: center;
}
</style>
