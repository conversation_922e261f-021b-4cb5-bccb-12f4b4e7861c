<template>
  <div class="card replicate-problem-card" style="margin-top: 24px">
    <div class="title">复现问题</div>
    <div class="description">复现信息</div>
    <el-form
      :model="formData"
      label-width="auto"
      class="form"
      :rules="submitRules"
      ref="submitRef"
      :disabled="!canEdit"
    >
      <el-form-item
        v-for="item in form"
        :key="item.label"
        :label="item.label"
        :label-position="'top'"
        class="form-item"
        :prop="item.key"
      >
        <el-input
          v-if="item.type === 'input'"
          :placeholder="item.placeholder"
          v-model="formData[item.key]"
          maxlength="100"
        />
        <el-select
          v-else-if="item.type === 'select'"
          :placeholder="item.placeholder"
          v-model="formData[item.key]"
        >
          <el-option
            v-for="option in item.options"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <el-input
          v-if="item.type === 'textarea'"
          type="textarea"
          :placeholder="item.placeholder"
          v-model="formData[item.key]"
          show-word-limit
          maxlength="2000"
        />
      </el-form-item>
    </el-form>
    <el-table class="table" :data="localDevices" style="width: 100%">
      <el-table-column
        :prop="'index'"
        type="index"
        :index="(index) => index + 1"
        :label="'序号'"
        :width="'88px'"
      />
      <el-table-column prop="deviceType">
        <template #header>
          <span><span class="required-field">*</span> 产品类型</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.deviceType'"
            style="margin-bottom: 0"
          >
            <el-select
              v-model="scope.row.deviceType"
              placeholder="请选择"
              @change="changeTypeInTable(scope.row)"
              :disabled="!canEdit"
            >
              <el-option
                v-for="option in offerType"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="deviceModel">
        <template #header>
          <span><span class="required-field">*</span> 产品机型</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.deviceModel'"
            style="margin-bottom: 0"
          >
            <el-select
              v-model="scope.row.deviceModel"
              placeholder="请选择"
              :disabled="!canEdit"
            >
              <el-option
                v-for="option in getPhoneListForRow(scope.row)"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="probability">
        <template #header>
          <span><span class="required-field">*</span> 复现概率</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.probability'"
            style="margin-bottom: 0"
          >
            <el-select
              v-model="scope.row.probability"
              placeholder="请选择"
              :disabled="!canEdit"
            >
              <el-option
                v-for="option in reproductionProbability"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="reproductionPath">
        <template #header>
          <span><span class="required-field">*</span> 复现路径</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.reproductionPath'"
            style="margin-bottom: 0"
          >
            <el-input
              v-model="scope.row.reproductionPath"
              placeholder="请输入内容"
              :disabled="!canEdit"
              maxlength="100"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="应用版本" prop="appVersion">
        <template #default="scope">
          <el-input
            v-model="scope.row.appVersion"
            placeholder="请输入内容"
            :disabled="!canEdit"
            maxlength="100"
          />
        </template>
      </el-table-column>
      <el-table-column label="OS版本" prop="osVersion">
        <template #default="scope">
          <el-input
            v-model="scope.row.osVersion"
            placeholder="请输入内容"
            :disabled="!canEdit"
            maxlength="100"
          />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!flowInfo.isAllFinished && canEdit"
        fixed="right"
        label="操作"
        min-width="120"
      >
        <template #default="scope">
          <el-icon
            :disabled="localDevices.length <= 1"
            @click="handleDelete(scope.$index)"
          >
            <Delete />
          </el-icon>
          <el-icon
            v-if="scope.$index === localDevices.length - 1"
            link
            type="primary"
            size="small"
            @click="addRow"
            style="margin-left: 16px; color: rgb(10, 89, 247)"
          >
            <Plus />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>

    <div class="description" v-if="!node.isFinished">操作信息</div>
    <el-form
      ref="formRef"
      :inline="true"
      :model="assignmentFormData"
      :rules="rulesAssignment"
      label-width="95"
      label-position="top"
      :disabled="!canEdit"
      style="margin-top: 24px; display: flex; align-items: center"
      v-if="!node.isFinished"
    >
      <el-form-item
        label="操作类型"
        prop="operationType"
        style="margin-right: 20px"
      >
        <el-radio-group
          v-model="assignmentFormData.operationType"
          @change="changeOperationType"
        >
          <el-radio value="0001">完成</el-radio>
          <el-radio value="0000">转单</el-radio>
          <el-radio value="0010">待观察</el-radio>
          <el-radio value="1000">非问题</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="指定处理人"
        prop="handler"
        v-if="
          !(
            assignmentFormData.operationType === '1000' ||
            assignmentFormData.operationType === '0010'
          )
        "
        style="margin-left: 40px; width: 25%"
      >
        <el-select
          placeholder="请选择内容"
          v-model="assignmentFormData.handler"
          filterable
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="operate-button-container text-center" v-if="canEdit">
      <el-button
        v-if="isNodeFinished"
        id="save-button"
        @click="saveReplicate"
        >{{ "更新信息" }}</el-button
      >
      <el-button
        v-if="!isNodeFinished"
        id="save-button"
        @click="saveReplicate"
        >{{ "保存" }}</el-button
      >
      <el-button
        id="assign-button"
        @click="assignmentSubmit"
        v-if="!isNodeFinished"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, toRefs } from "vue";
import {
  submitRules,
  rulesAssignment,
  levelOptions,
  degreeOptions,
} from "@/business/progressInformation/replicateProblem";
import {
  queryReproductionDevices,
  submitIssueReproduction,
  queryStepInfo,
} from "@/business/progressInformation/delimitProblem";
import {
  getHandlers,
  needGetHandlers,
} from "@/business/progressInformation/common/getHandlers";
import {
  offerType,
  reproductionProbability,
  offeringListByType,
  classificationOptions,
} from "@/components/issuesOpinionManage/commonArea/checkList";
import { ElMessageBox, ElMessage, ElLoading, ElDialog } from "element-plus";
import type { FormInstance } from "element-plus";
import { useRoute } from "vue-router";
import { useFlowInfoStore } from "@/store";
import { Delete, Plus } from "@element-plus/icons-vue";
import { useEventBus } from "@/hooks/useEventBus";
import { cloneDeep, isEqual } from "lodash-es";
import {validateTable} from "@/business/progressInformation/common/validateUtil";
import {ewpService as service} from "@/utils/axios";
const route = useRoute();

const emit = defineEmits(["refresh"]);

const refreshParentData = (step = null) => {
  emit("refresh", step);
};

const props = defineProps({
  canEdit: {
    required: true,
    default: () => false,
  },
  node: {
    default: () => {},
    required: true,
  },
});

const { node } = toRefs(props);
const isNodeFinished = node.value.isFinished;
const { flowInfo } = useFlowInfoStore();

// 获取URL参数中的opinionIssueId
const getOpinionIssueId = () => {
  return route.query.id;
};

const opinionIssueId = getOpinionIssueId();

// 表格表单验证规则
const tableRules = {
  deviceType: [
    { required: true, message: "请选择产品类型", trigger: "change" },
  ],
  deviceModel: [
    { required: true, message: "请选择产品机型", trigger: "change" },
  ],
  probability: [
    { required: true, message: "请选择复现概率", trigger: "change" },
  ],
  reproductionPath: [
    { required: true, message: "请输入复现路径", trigger: "blur" },
  ],
};

const curIndex = ref(1);

const form = [
  {
    type: "select",
    label: "问题等级",
    key: "level",
    required: true,
    placeholder: "请选择内容",
    options: levelOptions,
  },
  {
    type: "select",
    label: "严重程度",
    key: "degree",
    required: true,
    placeholder: "请选择内容",
    options: degreeOptions,
  },
  {
    type: "select",
    label: "问题分类",
    key: "classification",
    required: true,
    placeholder: "请选择内容",
    options: classificationOptions,
  },
  {
    type: "input",
    label: "关联子单",
    key: "relateOrder",
    placeholder: "请输入内容",
  },
  {
    type: "textarea",
    label: "问题描述",
    key: "description",
    required: true,
    placeholder: "请输入内容",
  },
];

const formData = reactive({
  level: "",
  degree: "",
  classification: "",
  relateOrder: "",
  description: "",
});

const localDevices = ref([]);

onMounted(async () => {
  if (opinionIssueId) {
    await fetchStepInfo();
  }
});

const eventBus = useEventBus();

const originData = ref({});

const fetchStepInfo = async () => {
  try {
    const response = await queryStepInfo(opinionIssueId);
    if (response) {
      formData.level = response.opinionIssueLevel;
      formData.degree = response.severity;
      formData.classification = response.faultCategory;
      formData.relateOrder = response.childIssue;
      formData.description = response.description;
      if (response.operateType === "0010") {
        eventBus.emit("updateReplicateColor", true);
      }
      if (response && response.reproductionDeviceInfoList) {
        localDevices.value = response.reproductionDeviceInfoList; // 深拷贝
        if (localDevices.value.length === 0) {
          addRow();
        }
      }
      originData.value = cloneDeep({
        ...formData,
        localDevices: localDevices.value,
      });
    }
  } catch (error) {
    console.error("获取复现设备数据失败:", error);
    ElMessage({
      type: "error",
      message: "获取复现设备数据失败",
    });
  }
};

// 添加新行
const addRow = () => {
  curIndex.value++;
  const newRow = {
    opinionIssueId,
    deviceType: "",
    deviceModel: "",
    probability: "",
    appVersion: "",
    osVersion: "",
    reproductionPath: "",
  };
  localDevices.value.push(newRow);
};

const getPhoneListForRow = (row) => {
  return offeringListByType(row.deviceType);
};

const changeTypeInTable = (row) => {
  row.deviceModel = "";
};

const handleDelete = (index: number) => {
  if (localDevices.value.length <= 1) {
    ElMessage({
      type: "warning",
      message: "至少需要保留一条复现条件",
    });
    return;
  }
  ElMessageBox.confirm("确认删除该复现条件?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    localDevices.value.splice(index, 1);

    ElMessage({
      type: "success",
      message: "删除成功",
    });
  });
};

// 保存所有数据
const saveReplicate = () => {
  if (!submitRef.value) return;
  // 验证表单
  Promise.all([submitRef.value.validate()])
    .then(async () => {
      try {
        // 准备提交数据
        const deviceList = localDevices.value;

        // 验证localDevices必填项
        const hasEmptyFields = deviceList.some(
          (item) =>
            !item.deviceType ||
            !item.deviceModel ||
            !item.probability ||
            !item.reproductionPath
        );

        if (hasEmptyFields) {
          ElMessage({
            type: "error",
            message: "请完成产品类型、产品机型、复现概率和复现路径的填写",
          });
          return;
        }

        if (
          node.value.isFinished &&
          isEqual({ ...formData, localDevices: deviceList }, originData.value)
        ) {
          ElMessage({
            type: "error",
            message: "内容未修改,请修改内容后更新",
          });
          return;
        }
        if(node.value.isFinished) {
          const params = {
            opinionIssueId,
            opinionIssueLevel: formData.level,
            severity: formData.degree,
            faultCategory: formData.classification,
            childIssue: formData.relateOrder,
            description: formData.description,
            operateType: '0001',
            reproductionDeviceInfoList: deviceList,
          }
          // 调用提交API
          await service.post('/issueReproduction/submit', params, {
            headers: {
              'Content-Type': 'application/json',
            },
          });
        }else{
          const params = {
            opinionIssueId,
            opinionIssueLevel: formData.level,
            severity: formData.degree,
            faultCategory: formData.classification,
            childIssue: formData.relateOrder,
            description: formData.description,
            reproductionDeviceInfoList: deviceList,
          }
          // 调用提交API
          await service.post('/issueReproduction/save', params, {
            headers: {
              'Content-Type': 'application/json',
            },
          });
        }
        refreshParentData(node.value.step);
        // 显示成功提示
        ElMessage({
          type: "success",
          message: node.value.isFinished ? "更新成功" : "保存成功",
        });
      } catch (error) {
        console.error("保存失败:", error);
        ElMessage({
          type: "error",
          message: node.value.isFinished ? "更新失败" : "保存失败",
        });
      }
    })
    .catch(() => {
      // 验证失败
      ElMessage({
        type: "warning",
        message: "请完成所有必填项",
      });
    });
};

const assignmentFormData = ref({
  operationType: "",
  handler: "",
});
const formRef = ref<FormInstance>();
const submitRef = ref<FormInstance>();

// 处理人选项列表
const options = ref([]);

// 改变操作类型
const changeOperationType = async (operateType) => {
  if (!needGetHandlers(operateType)) {
    return;
  }
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let handlerList = null;
  if (opinionIssueId) {
    handlerList = await getHandlers(opinionIssueId, operateType);
  }
  if (handlerList) {
    options.value = handlerList.handlers || [];
    assignmentFormData.value.handler = handlerList.defaultHandler || "";
  } else {
    ElMessage({
      type: "error",
      message: "获取下一步处理人失败",
    });
  }
  loading.close();
};

const assignmentSubmit = () => {
  if (!formRef.value || !submitRef.value) return;

  // Skip validation if operation type is transfer order (0000)
  if (assignmentFormData.value.operationType === "0000") {
    if(!assignmentFormData.value.handler) {
      ElMessage({
        type: "error",
        message: "请输入指定处理人",
      });
      return
    }
    submitWithoutValidation();
    return;
  }

  // Validate two forms for other operation types
  Promise.all([formRef.value.validate(), submitRef.value.validate()])
    .then(async () => {
      try {
        // 准备提交数据
        const deviceList = localDevices.value;

        // 验证localDevices必填项
        const hasEmptyFields = deviceList.some(
          (item) =>
            !item.deviceType ||
            !item.deviceModel ||
            !item.probability ||
            !item.reproductionPath||
        !validateTable(item.deviceType) ||
        !validateTable(item.deviceModel) ||
        !validateTable(item.probability) ||
        !validateTable(item.reproductionPath)
        );

        if (hasEmptyFields) {
          ElMessage({
            type: "error",
            message: "请完成产品类型、产品机型、复现概率和复现路径的填写",
          });
          return;
        }

        // 调用提交API
        await submitIssueReproduction({
          opinionIssueId,
          severity: formData.degree,
          opinionIssueLevel: formData.level,
          childIssue: formData.relateOrder,
          faultCategory: formData.classification,
          operateType: assignmentFormData.value.operationType,
          description: formData.description,
          reproductionDeviceInfoList: deviceList,
          assignedPerson: assignmentFormData.value.handler,
        });

        refreshParentData();
        // 显示成功提示
        ElMessage({
          type: "success",
          message: "提交成功",
        });
      } catch (error) {
        console.error("提交失败:", error);
        ElMessage({
          type: "error",
          message: "提交失败",
        });
      }
    })
    .catch(() => {
      // 验证失败
      ElMessage({
        type: "warning",
        message: "请完成所有必填项",
      });
    });
};

const submitWithoutValidation = async () => {
  try {
    const deviceList = localDevices.value;

    await submitIssueReproduction({
      opinionIssueId,
      opinionIssueLevel: formData.level,
      severity: formData.degree,
      faultCategory: formData.classification,
      childIssue: formData.relateOrder,
      description: formData.description,
      operateType: assignmentFormData.value.operationType,
      assignedPerson: assignmentFormData.value.handler,
      reproductionDeviceInfoList: deviceList,
    });

    refreshParentData();
    // 显示成功提示
    ElMessage({
      type: "success",
      message: "提交成功",
    });
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage({
      type: "error",
      message: "提交失败",
    });
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/common-styles.scss";
.replicate-problem-card {
  .form {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /* 4列，每列等宽 */
    grid-template-rows: auto auto;
    /* 两行，高度自适应  */
    gap: 10px;
    /* 元素之间的间距 */
    margin-top: 16px;

    .form-item {
      text-align: left;
      grid-column: span 1;

      /* 每个元素占1列 */
      &:nth-child(5) {
        grid-column: span 4;
        /* 第二行的元素占4列 */
      }

      .el-form-item__label {
        width: 100%;
      }
    }
  }

  .add-button-container {
    display: flex;
    margin-top: -2px;

    button {
      color: rgb(25, 25, 25);
      font-family: 微软雅黑;
      font-size: 14px;
      font-weight: 400;
      line-height: 150%;
      letter-spacing: 0%;
      text-align: left;
    }
  }

  .table {
    margin-top: 16.5px;
  }

  .operate-button-container {
    margin-top: 16px;

    #save-button {
      width: 96px;
    }

    #assign-button {
      font-family: 微软雅黑;
      font-size: 14px;
      font-weight: 400;
      line-height: 150%;
      letter-spacing: 0%;
      text-align: left;
      width: 96px;
    }

    #save-button {
      color: rgb(25, 25, 25);
    }

    #assign-button {
      color: rgb(255, 255, 255);
      background: rgb(10, 89, 247);
    }
  }
}

.dialog-form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 2列，每列等宽 */
  gap: 20px;
  /* 元素之间的间距 */
}

.full-width {
  grid-column: span 2;
  /* 占满整行 */
  margin-top: 20px;
}

/* 添加必填项红色星号样式 */
.required-field {
  color: #f56c6c;
  margin-left: 4px;
}

:deep(.el-form-item__error) {
  position: absolute;
  top: 100%;
  left: 0;
  padding-top: 2px;
  font-size: 12px;
  color: #f56c6c;
}

.el-icon {
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
    color: gray;
  }
}
</style>
