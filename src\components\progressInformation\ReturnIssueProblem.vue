<template>
  <div class="card relate-order-card" style="margin-top: 20px">
    <div class="title">回归问题</div>
    <div class="description">回归信息</div>
    <el-table class="table" :data="localDevices" style="width: 100%">
      <el-table-column
        :prop="'index'"
        type="index"
        :index="(index) => index + 1"
        :label="'序号'"
        :width="'88px'"
      />
      <el-table-column prop="deviceType">
        <template #header>
          <span><span class="required-field">*</span> 产品类型</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.deviceType'"
            style="margin-bottom: 0"
          >
            <el-select
              v-model="scope.row.deviceType"
              placeholder="请选择"
              @change="changeTypeInTable(scope.row)"
              :disabled="true"
            >
              <el-option
                v-for="option in offerType"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="deviceModel">
        <template #header>
          <span><span class="required-field">*</span> 产品机型</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.deviceModel'"
            style="margin-bottom: 0"
          >
            <el-select
              v-model="scope.row.deviceModel"
              placeholder="请选择"
              :disabled="true"
            >
              <el-option
                v-for="option in getPhoneListForRow(scope.row)"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="probability">
        <template #header>
          <span><span class="required-field">*</span> 复现概率</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.probability'"
            style="margin-bottom: 0"
          >
            <el-select
              v-model="scope.row.probability"
              placeholder="请选择"
              :disabled="true"
            >
              <el-option
                v-for="option in reproductionProbability"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="reproductionPath">
        <template #header>
          <span><span class="required-field">*</span> 复现路径</span>
        </template>
        <template #default="scope">
          <el-form-item
            :prop="'rows.' + scope.$index + '.reproductionPath'"
            style="margin-bottom: 0"
          >
            <el-input
              v-model="scope.row.reproductionPath"
              placeholder="请输入内容"
              :disabled="true"
              maxlength="100"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="应用版本" prop="appVersion">
        <template #default="scope">
          <el-input
            v-model="scope.row.appVersion"
            placeholder="请输入内容"
            :disabled="true"
            maxlength="100"
          />
        </template>
      </el-table-column>
      <el-table-column label="OS版本" prop="osVersion">
        <template #default="scope">
          <el-input
            v-model="scope.row.osVersion"
            placeholder="请输入内容"
            :disabled="true"
            maxlength="100"
          />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="回归结论" min-width="120">
        <template #default="scope">
          <el-select
            v-model="scope.row.status"
            :disabled="!canEdit || isNodeFinished"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="option in conclusionOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>

    <el-form
      :model="progressData"
      class="form"
      label-width="auto"
      :disabled="!canEdit"
      :rules="progressRules"
      ref="progressRef"
    >
      <el-form-item
        label="回归详情"
        :label-position="'top'"
        class="form-item"
        prop="detail"
      >
        <el-input
          type="textarea"
          placeholder="请输入内容"
          v-model="progressData.detail"
          show-word-limit
          :rows="4"
          maxlength="2000"
        />
      </el-form-item>
    </el-form>
    <div class="description" v-if="!isNodeFinished && canEdit">操作信息</div>
    <el-form
      v-if="!isNodeFinished && canEdit"
      ref="formRef"
      :inline="true"
      :model="formData"
      :rules="rulesAssignment"
      label-width="95"
      label-position="top"
      style="margin-top: 20px; display: flex; align-items: center"
    >
      <el-form-item
        prop="operationType"
        class="form-item"
        style="margin-right: 20px"
        label="操作类型"
      >
        <el-radio-group
          @change="changeOperationType"
          v-model="formData.operationType"
        >
          <el-radio :value="operationTypeOptions[1].value">{{
            operationTypeOptions[1].label
          }}</el-radio>
          <el-radio :value="operationTypeOptions[0].value">{{
            operationTypeOptions[0].label
          }}</el-radio>
          <el-radio :value="operationTypeOptions[2].value">{{
            operationTypeOptions[2].label
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="指定处理人"
        v-if="formData.operationType !== '0002'"
        class="form-item"
        prop="nextHandler"
        style="margin-left: 24px; width: 20%"
      >
        <el-select v-model="formData.nextHandler" filterable>
          <el-option
            v-for="item in handlerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="canEdit" class="operate-button-container text-center">
      <el-button id="save-button" @click="onSave">{{
        isNodeFinished ? "更新信息" : "保存"
      }}</el-button>
      <el-button
        v-if="!isNodeFinished"
        id="assign-button"
        type="primary"
        @click="onSubmit"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, toRefs } from "vue";
import { useRoute } from "vue-router";
import { ElLoading, ElMessage, type FormInstance } from "element-plus";
import { operationTypeOptions } from "@/business/progressInformation/delimitProblem";
import { rulesAssignment } from "@/business/progressInformation/replicateProblem";
import { getHandlers } from "@/business/progressInformation/common/getHandlers";
import { ewpService } from "@/utils/axios";
import { useFlowInfoStore } from "@/store";
import {
  offeringListByType,
  offerType,
  reproductionProbability,
} from "@/components/issuesOpinionManage/commonArea/checkList";
import { getValidateFun } from "@/business/progressInformation/common/validateUtil";

const emit = defineEmits(["refresh"]);

const refreshParentData = (step = null) => {
  emit("refresh", step);
};
const props = defineProps({
  canEdit: {
    default: () => false,
    required: true,
  },
  node: {
    required: true,
    default: () => {},
  },
});

const changeTypeInTable = (row) => {
  row.deviceModel = "";
};

const getPhoneListForRow = (row) => {
  return offeringListByType(row.deviceType);
};

const localDevices = ref([]);

const conclusionOptions = [
  { label: "通过", value: 1 },
  { label: "不通过", value: 0 },
];

const progressRef = ref<FormInstance>();

const formRef = ref<FormInstance>();

const validateFun = (rule, value, callback) => {
  if (
    value.trim() === "" ||
    /【产品机型】\s*\n【应用版本】\s*\n【OS版本】\s*\n【详细过程】\s*/.test(
      value
    )
  ) {
    callback && callback(new Error("回归详情不能为空！"));
    return false;
  }
  callback && callback();
  return true;
};

const progressRules = {
  detail: [
    { required: true, message: "回归详情必填！" },
    { validator: validateFun, trigger: "blur" },
  ],
};

const flowInfoStore = useFlowInfoStore();

const { node } = toRefs(props);
const isNodeFinished = node.value.isFinished;

const route = useRoute();
const opinionIssueId = route.query.id;

const tableData = ref([]);
const handlerOptions = ref([]);

const progressData = reactive({
  conclusion: "",
  detail: "【产品机型】\n【应用版本】\n【OS版本】\n【详细过程】",
});

const formData = reactive({
  operationType: "",
  nextHandler: "",
});

const changeOperationType = async (operationType) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let handlerList = null;
  if (opinionIssueId) {
    handlerList = await getHandlers(opinionIssueId, operationType);
  }
  if (handlerList) {
    handlerOptions.value = handlerList.handlers;
    formData.nextHandler = handlerList.defaultHandler;
  } else {
    ElMessage({
      type: "error",
      message: "获取下一步处理人失败",
    });
  }
  loading.close();
};

onMounted(() => {
  fetchExistingData();
});

const checkAllPass = () => {
  let pass = true;
  const noPassList = [];
  localDevices.value.forEach((item, index) => {
    if (item.status === 0) {
      noPassList.push(index + 1);
      pass = false;
    }
  });
  return { pass, noPassList };
};

const onSubmit = async () => {
  if (!formRef.value) return;
  if (!progressRef.value) return;
  if (formData.operationType === "0002") {
    try {
      const requestData = {
        opinionIssueId: opinionIssueId,
        nextHandler: flowInfoStore.getLastPerson(5),
        operateType: formData.operationType,
        returnDetail: progressData.detail,
        reproductionDeviceInfoList: localDevices.value,
      };
      const { pass, noPassList } = checkAllPass();
      if (pass) {
        ElMessage({
          type: "error",
          message: `结论全部通过，无法驳回！`,
        });
        return;
      }
      await ewpService.post("/returnOpinionIssue/submit", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "驳回成功",
      });
      refreshParentData();
    } catch (err) {
      ElMessage({
        type: "error",
        message: "驳回失败",
      });
    }
  } else if (formData.operationType === "0000") {
    formRef.value.validate(async (valid2, fields2) => {
      try {
        if (valid2) {
          const requestData = {
            operateType: formData.operationType,
            opinionIssueId: opinionIssueId,
            nextHandler: formData.nextHandler,
            reproductionDeviceInfoList: localDevices.value,
            returnDetail: progressData.detail,
          };
          await ewpService.post("/returnOpinionIssue/submit", requestData, {
            headers: {
              "Content-Type": "application/json",
            },
          });
          ElMessage({
            type: "success",
            message: "转单成功",
          });
          refreshParentData();
        }
      } catch (err) {
        ElMessage({
          type: "error",
          message: "转单失败",
        });
      }
    });
  } else {
    progressRef.value.validate(async (valid, fields) => {
      formRef.value.validate(async (valid2, fields2) => {
        try {
          if (valid && valid2) {
            const { pass, noPassList } = checkAllPass();
            if (pass) {
              const requestData = {
                operateType: formData.operationType,
                returnDetail: progressData.detail,
                reproductionDeviceInfoList: localDevices.value,
                nextHandler: formData.nextHandler,
                opinionIssueId: opinionIssueId,
              };
              await ewpService.post("/returnOpinionIssue/submit", requestData, {
                headers: {
                  "Content-Type": "application/json",
                },
              });
              ElMessage({
                type: "success",
                message: "提交成功",
              });
              refreshParentData();
            } else {
              ElMessage({
                type: "error",
                message: `第${noPassList.join("条,")}条结论不通过,无法提交`,
              });
            }
          }
        } catch (err) {
          ElMessage({
            type: "error",
            message: "提交失败",
          });
        }
      });
    });
  }
};

const onSave = async () => {
  if (!formRef.value) return;
  if (!progressRef.value) return;
  if (node.value.isFinished) {
    try {
      const requestData = {
        opinionIssueId: opinionIssueId,
        nextHandler: formData.nextHandler,
        operateType: "0001",
        returnConclusion: progressData.conclusion,
        returnDetail: progressData.detail,
      };
      await ewpService.post("/returnOpinionIssue/submit", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "更改成功",
      });
      refreshParentData(node.value.step);
    } catch (err) {
      ElMessage({
        type: "error",
        message: "更改失败",
      });
    }
  } else {
    try {
      const requestData = {
        opinionIssueId: opinionIssueId,
        nextHandler: formData.nextHandler,
        operateType: formData.operationType,
        returnConclusion: progressData.conclusion,
        returnDetail: progressData.detail,
      };

      await ewpService.post("/returnOpinionIssue/save", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "保存成功",
      });
      refreshParentData(node.value.step);
    } catch (err) {
      ElMessage({
        type: "error",
        message: "保存失败",
      });
    }
  }
};

// 获取已有数据
const fetchExistingData = async () => {
  if (!opinionIssueId) return;

  const response = await ewpService.get(
    `/returnOpinionIssue/queryMaxVersionData?opinionIssueId=${opinionIssueId}`
  );
  if (response) {
    progressData.conclusion = response.returnConclusion;
    progressData.detail =
      response.returnDetail ||
      "【产品机型】\n【应用版本】\n【OS版本】\n【详细过程】";
    localDevices.value = response.reproductionDeviceInfoList;
  }
};
</script>

<style scoped lang="scss">
@import "@/styles/common-styles.scss";
</style>
