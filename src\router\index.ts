import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus';
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { isDevMode, UNAUTHORIZED_PAGE } from '@/utils/env';
import { clearCsrfToken } from '@/utils/axios';

// 白名单路由
const whiteList: string[] = ['/login', '/403', '/404'];
const title = import.meta.env.VITE_GLOB_APP_TITLE

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title,
      requiresAuth: false
    }
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/exception/403.vue'),
    meta: {
      title,
      requiresAuth: false
    }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/exception/404.vue'),
    meta: {
      title,
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/layouts/MainLayout.vue'),
    redirect: '/issues-opinion-manage',
    children: [
      {
        path: '/todo',
        name: 'MyToDo',
        meta: {
          title,
          resourceId: 'MyTodo'
        },
        component: () => import('@/components/issuesOpinionManage/mytodo/index.vue'),
      },
      {
        path: '/issues-opinion-manage',
        name: 'IssuesOpinionManage',
        component: () => import('@/components/issuesOpinionManage/problemManage/index.vue'),
        meta: {
          title,
          resourceId: 'IssuesOpinionManage'
        }
      },
      {
        path: '/opinion-management-detail',
        name: 'OpinionManagementDetail',
        component: () => import('@/views/optionDetail/index.vue'),
        meta: {
          title,
          resourceId: 'OpinionManagementDetail'
        }
      },
      {
        path: '/statistical-dashboard',
        name: 'StatisticalDashboard',
        component: () => import('@/views/statisticalDashboard/index.vue'),
        meta: {
          title,
          requiresAuth: true,
          resourceId: 'StatisticalDashboard'
        },
      },
      {
        path: '/source-scene-manage',
        name: 'sourceSceneManage',
        component: () => import('@/views/sourceScene/sourceSceneManage/index.vue'),
        meta: {
          title,
          requiresAuth: true,
          resourceId: 'SourceSceneManage'
        },
      },
      {
        path: '/source-scene-detail',
        name: 'sourceSceneDetail',
        component: () => import('@/views/sourceScene/sourceSceneDetail/index.vue'),
        meta: {
          title,
          requiresAuth: true,
          resourceId: 'SourceSceneDetail'
        },
      },
      {
        path: '/meta-manager',
        name: 'MetaManager',
        component: () => import('@/views/metaManager/index.vue'),
        meta: {
          title,
          requiresAuth: true,
          resourceId: 'MetaManager'
        },
      },
      {
        path: '/source-sync-status',
        name: 'SourceSyncStatus',
        component: () => import('@/views/sourceSyncStatus/index.vue'),
        meta: {
          title,
          requiresAuth: true,
          resourceId: 'SourceSyncStatus'
        },
      },
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'any',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 检查路由是否在用户菜单权限中
function checkRouteInMenuList(path: string, menuList: any[]): boolean {
  // 开发模式下跳过权限检查
  if (isDevMode()) {
    return true;
  }

  if (!menuList || menuList.length === 0) {
    return false;
  }

  // 获取当前路由对应的resourceId
  const route = router.getRoutes().find(route => route.path === path);
  if (!route || !route.meta.resourceId) {
    return false;
  }

  const routeResourceId = route.meta.resourceId as string;

  // 检查当前菜单项
  const found = menuList.some(menu => {
    // 检查当前菜单的resourceId是否匹配
    if (menu.resourceId === routeResourceId) {
      return true;
    }

    // 递归检查子菜单
    if (menu.children && menu.children.length > 0) {
      return checkRouteInMenuList(path, menu.children);
    }

    return false;
  });

  return found;
}

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title}` : 'IssuesOpinionManage';

  // 如果是白名单路由，直接放行
  if (whiteList.includes(to.path)) {
    next();
    return;
  }

  // 开发模式下跳过登录和权限检查
  if (isDevMode()) {
    next();
    return;
  }

  const userStore = useUserStore();

  // 检查用户是否登录
  try {
    const res = await userStore.checkLogin();
    if (res && res.resultCode !== 0) {
      clearCsrfToken();
      // 未登录，跳转到登录页
      ElMessage.error('未登录，跳转到登录页');
      window.location.href = UNAUTHORIZED_PAGE
      return;
    }

    // 获取用户信息和权限
    if (!userStore.userInfo || !userStore.userInfo.userId) {
      await userStore.getUserInfo();
    }

    // 获取用户角色列表
    if (!userStore.roleList || userStore.roleList.length === 0) {
      await userStore.getUserRoleList();
    }

    // 获取菜单权限
    if (!userStore.menuList || userStore.menuList.length === 0) {
      await userStore.getUserPermissions();
    }

    // 检查路由是否在用户菜单权限中
    if (checkRouteInMenuList(to.path, userStore.menuList)) {
      next();
    } else {
      // 无权限访问该路由，跳转到403页面
      next({ path: '/403' });
    }
  } catch (error) {
    console.error('路由守卫错误:', error);
  }
})

export default router 
