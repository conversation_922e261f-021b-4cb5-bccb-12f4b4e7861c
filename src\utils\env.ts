
// Development model
export const devMode = 'development';

// Production mode
export const prodMode = 'production';


let realHref = window.location.href;

// 1. 本地需要真实登录信息,返回false
// 2. 本地不需要真实登录信息,返回true
export const mock = false;

export const PROUNAUTHORIZED_PAGE = `${location.origin}?redirect=${encodeURIComponent(realHref)}`

export const UNAUTHORIZED_PAGE = isDev()
  ? '/#/login'
  : PROUNAUTHORIZED_PAGE;

export const WO_AUTH = 'MTAwN0IxNzRFRTY2MzQ4QzQzNjBDRTZEOUEzMjQxRkY6N0E4NjlEQjY0MTM3NDY5NDYzNzQ5QUJCM0Y2NzFCMDEyM0IyMERDREMwRTU0OTRBREQwQzk3OTNFRTZDQTNDNjgxQTlBNUE2MjAyNkM4NTlERTU3QTYyNEI3RjIyMzE1QjcwMTIzQjNBMTA4MkNGQjRBODlGODBBNzhGOThDODM='

// Get environment variables
export function getEnv(): string {
  return import.meta.env.MODE;
}

// Is it a development mode
export function isDevMode(): boolean {
  return import.meta.env.DEV && mock;
}

// Is it a production mode
export function isProdMode(): boolean {
  return import.meta.env.PROD;
}


// Is it a development mode
export function isDev(): boolean {
  return import.meta.env.DEV;
}
