
// Development model
export const devMode = 'development';

// Production mode
export const prodMode = 'production';


let realHref = window.location.href;

// 1. 本地需要真实登录信息,返回false
// 2. 本地不需要真实登录信息,返回true
export const mock = false;

export const PROUNAUTHORIZED_PAGE = `${location.origin}?redirect=${encodeURIComponent(realHref)}`

export const UNAUTHORIZED_PAGE = isDev()
  ? '/#/403'
  : PROUNAUTHORIZED_PAGE;

export const WO_AUTH = 'OEQyNDI2ODY0NjQ5QjlDMjFBMkVENkJDMEEwQjY0NEY6Q0QyODMyMTZGQTk4RkQ2QkJBRDdGMEFDNTY1QkZFRkQ1MTNGNjE1RTA4RDVCQjAzMTczOUQ1QkY2NDZENjlENTc4NDVGOTUyRjgwRTM0MzUyNzhDQjdCRTlGQ0JFMkNGMzcwMjc3MzM1NzZCNTE5N0UwMzFFMTc4MzkyN0M3Qzc='

// Get environment variables
export function getEnv(): string {
  return import.meta.env.MODE;
}

// Is it a development mode
export function isDevMode(): boolean {
  return import.meta.env.DEV && mock;
}

// Is it a production mode
export function isProdMode(): boolean {
  return import.meta.env.PROD;
}


// Is it a development mode
export function isDev(): boolean {
  return import.meta.env.DEV;
}
