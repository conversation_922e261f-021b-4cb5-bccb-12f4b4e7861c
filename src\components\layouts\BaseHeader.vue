<script lang="ts" setup>
import { UserRoleEnum } from "@/enums/UserRoleEnum";
import { useUserStore } from "@/store/modules/user";
import { SwitchButton, ArrowDown } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { computed, ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { isDevMode } from "@/utils/env";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 用户信息
const userInfo = computed(() => userStore.userInfo);
const userName = computed(
  () => userInfo.value.cnNameEmpNo || userInfo.value.enNameEmpNo || "用户"
);
const userEmail = computed(() => userInfo.value.email || "");
const userInitial = computed(() => {
  const name = userName.value;
  return name ? name.charAt(0) : "用";
});

// 菜单项类型定义
interface MenuItem {
  name: string;
  path: string;
  resourceId: string;
  children?: MenuItem[];
  permissions?: string[];
}

// 用户权限资源类型定义
interface UserResource {
  resourceId: string;
  resoWebUrl?: string;
  [key: string]: any;
}

// 定义导航菜单
const navMenus = ref<MenuItem[]>([
  {
    name: "我的待办",
    path: "/todo",
    resourceId: "MyTodo",
  },
  {
    name: "源声场景管理",
    path: "/source-scene-manage",
    resourceId: "SourceSceneManage",
  },
  {
    name: "问题管理",
    path: "/issues-opinion-manage",
    permissions: [],
    resourceId: "IssuesOpinionManage",
  },
  {
    name: "数据看板",
    path: "/dataview",
    permissions: [],
    resourceId: "DataDashboard",
    children: [
      {
        name: "统计看板",
        path: "/statistical-dashboard",
        permissions: [],
        resourceId: "StatisticalDashboard",
      },
      {
        name: "元数据管理",
        path: "/meta-manager",
        permissions: [],
        resourceId: "MetaManager",
      },
      {
        name: "源声同步状态",
        path: "/source-sync-status",
        permissions: [],
        resourceId: "SourceSyncStatus",
      },
    ],
  },
]);

const filteredMenus = computed<MenuItem[]>(() => {
  // 开发模式下显示所有菜单
  if (isDevMode()) {
    return navMenus.value;
  }

  const resources = userStore.menuList || ([] as UserResource[]);

  return navMenus.value
    .map((item) => {
      const hasPermission = resources.some(
        (resource) =>
          resource.resourceId === item.resourceId ||
          resource.resoWebUrl === item.path
      );

      if (item.children && item.children.length > 0) {
        const filteredChildren = item.children.filter((child) =>
          resources.some(
            (resource) =>
              resource.resourceId === child.resourceId ||
              resource.resoWebUrl === child.path
          )
        );

        if (hasPermission && filteredChildren.length > 0) {
          return {
            ...item,
            children: filteredChildren,
          };
        } else if (hasPermission) {
          return {
            ...item,
            children: [],
          };
        }
        return null;
      }

      return hasPermission ? item : null;
    })
    .filter((item): item is MenuItem => item !== null); // 过滤掉null值并指定类型
});

const activeMenu = ref(router.currentRoute.value.path);

const handleNavClick = (path: string) => {
  activeMenu.value = path;
};

const handleCommand = (command: string) => {
  if (command === "logout") {
    handleLogout();
  }
};

const handleLogout = async () => {
  try {
    await userStore.logout();
    ElMessage.success("退出登录成功");
  } catch (error) {
    ElMessage.error("退出登录失败");
  }
};

// 初始化获取用户信息和权限
onMounted(async () => {
  // 非开发模式下才获取用户信息和权限
  if (!isDevMode()) {
    if (!userStore.userInfo.userId) {
      await userStore.getUserInfo();
    }
    if (!userStore.menuList.length) {
      await userStore.getUserPermissions();
    }
  }
});
</script>

<template>
  <div class="header-container">
    <div class="logo" @click="router.push('/')">
      <span class="title">统一工单平台</span>
    </div>

    <div class="header-right">
      <el-menu
        mode="horizontal"
        :default-active="activeMenu"
        router
        :ellipsis="false"
      >
        <template v-for="item in filteredMenus" :key="item.path || item.name">
          <el-sub-menu
            v-if="item.children && item.children.length > 0"
            :index="item.path || item.name"
          >
            <template #title>{{ item.name }}</template>
            <el-menu-item
              v-for="child in item.children"
              :key="child.path"
              :index="child.path"
              @click="handleNavClick(child.path)"
            >
              {{ child.name }}
            </el-menu-item>
          </el-sub-menu>

          <el-menu-item
            v-else
            :index="item.path"
            @click="handleNavClick(item.path)"
          >
            {{ item.name }}
          </el-menu-item>
        </template>
      </el-menu>

      <el-dropdown @command="handleCommand">
        <div class="user-info">
          <el-avatar :size="32">
            {{ userInitial }}
          </el-avatar>
          <el-icon style="margin-left: 8px"><arrow-down /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item disabled>
              <div class="user-dropdown-info">
                <span>{{ userName }}</span>
                <small>{{ userEmail }}</small>
              </div>
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  background-color: var(--default-color);
  color: #ffffff;
  .title {
    font-size: 16px;
  }

  .logo {
    display: flex;
    align-items: center;
    cursor: pointer;

    .logo-icon {
      width: 32px;
      height: 32px;
      background-color: #1890ff;
      border-radius: 4px;
      margin-right: 12px;
    }

    h1 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: #ffffff;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    :deep(.el-menu--horizontal) {
      border-bottom: none;
      background-color: transparent;

      .el-menu-item,
      .el-sub-menu__title {
        color: #ffffff;
        border-bottom: 2px solid transparent;

        &:hover,
        &.is-active {
          color: #ffffff;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }

      .el-sub-menu:hover .el-sub-menu__title {
        color: #ffffff;
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.3s;
      color: #ffffff;

      .username {
        margin-left: 8px;
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }
}

.user-dropdown-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  small {
    color: #999;
    font-size: 12px;
  }
}

:deep(.dark) {
  .header-container {
    background-color: #000000;

    .logo h1 {
      color: #fff;
    }

    .nav-menu {
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        color: #ddd;

        &.is-active {
          color: #409eff;
          border-bottom-color: #409eff;
        }

        &:hover {
          color: #409eff;
        }
      }

      :deep(.el-sub-menu) {
        .el-sub-menu__title {
          color: #ddd;

          &:hover {
            color: #409eff;
          }
        }

        // 暗黑模式下子菜单弹出层样式
        .el-menu--popup {
          background-color: #1f1f1f;
          border: 1px solid #333;

          .el-menu-item {
            color: #ddd;

            &:hover {
              color: #409eff;
              background-color: #333;
            }
          }
        }
      }
    }

    .user-info:hover {
      background-color: #333;
    }
  }
}
</style>
