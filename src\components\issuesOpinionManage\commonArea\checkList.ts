export const opinionLevelist = [
    { label: "A", value: "A" },
    { label: "B", value: "B" },
    { label: "C", value: "C" },
    { label: "D", value: "D" },
    { label: "其他", value: "其他" }
]
export const problemSource = [
    { label: "VOC预警", value: "VOC预警" },
    { label: "服务热线", value: "服务热线" },
    { label: "服务门店", value: "服务门店" },
    { label: "线下零售门店", value: "线下零售门店" },
    { label: "回退问卷调研", value: "回退问卷调研" },
    { label: "NSS问卷调研", value: "NSS问卷调研" },
    { label: "应用市场评论", value: "应用市场评论" },
    { label: "心愿单", value: "心愿单" },
    { label: "反馈助手", value: "反馈助手" },
    { label: "BetaClub", value: "BetaClub" },
    { label: "花粉论坛", value: "花粉论坛" },
    { label: "互联网", value: "互联网" },
    { label: "产品线测试", value: "产品线测试" },
]
export const offeringList = [
    { label: "Mate X系列", value: "Mate X系列" },
    { label: "Mate 70系列", value: "Mate 70系列" },
    { label: "Mate 60系列", value: "Mate 60系列" },
    { label: "Pura 80系列", value: "Pura 80系列" },
    { label: "Pura X系列", value: "Pura X系列" },
    { label: "Nova14系列", value: "Nova14系列" },
    { label: "Nova Flip", value: "Nova Flip" },
    { label: "Nova 13系列", value: "Nova 13系列" },
    { label: "Nova 12系列", value: "Nova 12系列" },
    { label: "PC", value: "PC" }
]
export const offerType = [
    { label: "手机", value: "手机" },
    { label: "平板", value: "平板" },
    { label: "PC", value: "PC" },
    { label: "手表", value: "手表" },
]

export const offeringListByType = type => {
    if (!type) return []
    if (type === '手机') {
        return [
            { label: "Mate X系列", value: "Mate X系列" },
            { label: "Mate 70系列", value: "Mate 70系列" },
            { label: "Mate 60系列", value: "Mate 60系列" },
            { label: "Pura 80系列", value: "Pura 80系列" },
            { label: "Pura X系列", value: "Pura X系列" },
            { label: "Nova14系列", value: "Nova14系列" },
            { label: "Nova Flip", value: "Nova Flip" },
            { label: "Nova 13系列", value: "Nova 13系列" },
            { label: "Nova 12系列", value: "Nova 12系列" }
        ]
    } else if (type === '平板') {
        return [
            { label: "MatePad Pro系列", value: "MatePad Pro系列" },
        ]
    } else if (type === 'PC') {
        return [
            { label: "PC", value: "PC" },
        ]
    }
    return []
}

export const reproductionProbability = [
    { label: "有条件必然复现", value: "有条件必然复现" },
    { label: "有条件概率复现", value: "有条件概率复现" },
    { label: "无规律复现", value: "无规律复现" },
    { label: "很难复现", value: "很难复现" },
]
export const statusList = [
    { label: "问题识别", value: "0000" },
    { label: "问题定界", value: "0001" },
    { label: "计划锁定", value: "0002" },
    { label: "问题修复", value: "0003" },
    { label: "验证闭环", value: "0004" },
    { label: "问题关闭", value: "0005" }
]

export const stepList = [
    { label: "复现问题", value: "0000" },
    { label: "定界问题", value: "0001" },
    { label: "审核定界结论", value: "0002" },
    { label: "关联工单", value: "0003" },
    { label: "锁定解决计划", value: "0004" },
    { label: "更新答复口径", value: "0005" },
    { label: "确认问题修复版本", value: "0006" },
    { label: "回归问题", value: "0007" },
    { label: "更新答复口径", value: "0008" }
]

export const severityList = [
    { label: "致命", value: "致命" },
    { label: "严重", value: "严重" },
    { label: "一般", value: "一般" },
    { label: "提示", value: "提示" },
]

// 应用层级
export const appLayerList = [
    { label: "超级", value: "超级" },
    { label: "标杆", value: "标杆" },
    { label: "精品", value: "精品" },
    { label: "TOP225", value: "TOP225" },
    { label: "互联网与OA服务商底座", value: "互联网与OA服务商底座" },
    { label: "4314", value: "4314" },
    { label: "超级(PC)", value: "超级(PC)" },
    { label: "精品(PC)", value: "精品(PC)" },
    { label: "头部(PC)", value: "头部(PC)" },
    { label: "实验局(PC)", value: "实验局(PC)" },
    { label: "其他(PC)", value: "其他(PC)" },
    { label: "专项(PAD)", value: "专项(PAD)" },
    { label: "专项(智慧屏)", value: "专项(智慧屏)" },
    { label: "专项(穿戴)", value: "专项(穿戴)" },
    { label: "专项(车机)", value: "专项(车机)" },
    { label: "4796", value: "4796" },
    { label: "生态丰富度5165", value: "生态丰富度5165" },
    { label: "企业内部办公应用", value: "企业内部办公应用" },
    { label: "全省统一政务办公应用", value: "全省统一政务办公应用" },
    { label: "供应商", value: "供应商" },
    { label: "垂域专精", value: "垂域专精" },
    { label: "繁荣生态01", value: "繁荣生态01" },
    { label: "繁荣生态02", value: "繁荣生态02" },
    { label: "鼎桥", value: "鼎桥" },
    { label: "TOP32", value: "TOP32" },
    { label: "心愿单", value: "心愿单" },
    { label: "专业领域", value: "专业领域" },
    { label: "其他", value: "其他" },
    { label: "超级-鸿蒙电脑", value: "超级-鸿蒙电脑" },
    { label: "基础-鸿蒙电脑", value: "基础-鸿蒙电脑" },
    { label: "2B-鸿蒙电脑", value: "2B-鸿蒙电脑" },
    { label: "PAD独有", value: "PAD独有" },
    { label: "PC千行百业", value: "PC千行百业" },
];

// 纵队
export const columnList = [
    { label: "头部互联网", value: "头部互联网" },
    { label: "区域重点", value: "区域重点" },
    { label: "行业总部", value: "行业总部" },
    { label: "全场景", value: "全场景" },
];

export const addressList = [
    { label: "安徽", value: "安徽" },
    { label: "北京", value: "北京" },
    { label: "福建", value: "福建" },
    { label: "甘肃", value: "甘肃" },
    { label: "广东", value: "广东" },
    { label: "深圳", value: "深圳" },
    { label: "广西", value: "广西" },
    { label: "海南", value: "海南" },
    { label: "贵州", value: "贵州" },
    { label: "河北", value: "河北" },
    { label: "河南", value: "河南" },
    { label: "黑龙江", value: "黑龙江" },
    { label: "湖北", value: "湖北" },
    { label: "湖南", value: "湖南" },
    { label: "吉林", value: "吉林" },
    { label: "江苏", value: "江苏" },
    { label: "江西", value: "江西" },
    { label: "辽宁", value: "辽宁" },
    { label: "内蒙古", value: "内蒙古" },
    { label: "青海", value: "青海" },
    { label: "山东", value: "山东" },
    { label: "山西", value: "山西" },
    { label: "陕西", value: "陕西" },
    { label: "宁夏", value: "宁夏" },
    { label: "上海", value: "上海" },
    { label: "四川", value: "四川" },
    { label: "西藏", value: "西藏" },
    { label: "天津", value: "天津" },
    { label: "新疆", value: "新疆" },
    { label: "云南", value: "云南" },
    { label: "浙江", value: "浙江" },
    { label: "重庆", value: "重庆" },
    { label: "行业系统部", value: "行业系统部" },
    { label: "金融系统部", value: "金融系统部" },
    { label: "数字政府系统部", value: "数字政府系统部" },
    { label: "油气矿山系统部", value: "油气矿山系统部" },
    { label: "国网大客户部", value: "国网大客户部" },
    { label: "教育医疗系统部", value: "教育医疗系统部" },
    { label: "互联网传媒系统部", value: "互联网传媒系统部" },
    { label: "大企业系统部", value: "大企业系统部" },
    { label: "电力系统部", value: "电力系统部" },
    { label: "智能制造系统部", value: "智能制造系统部" },
    { label: "国铁大客户部", value: "国铁大客户部" },
    { label: "综合系统部", value: "综合系统部" },
    { label: "交通系统部", value: "交通系统部" },
    { label: "终端云", value: "终端云" },
    { label: "香港", value: "香港" },
    { label: "运营商", value: "运营商" },
    { label: "联通系统部", value: "联通系统部" },
    { label: "电信系统部", value: "电信系统部" },
    { label: "移动系统部", value: "移动系统部" },
    { label: "广电系统部", value: "广电系统部" },
    { label: "铁塔系统部", value: "铁塔系统部" },
    { label: "海外", value: "海外" }
]
export const defaulList = [
    { label: "问题等级", key: "opinionIssueLevel", list: opinionLevelist },
    { label: "问题来源", key: "source", list: problemSource },
    { label: "当前阶段", key: "status", list: statusList },
]
export const defaultMineList = [
    { label: "问题等级", key: "opinionIssueLevel", list: opinionLevelist },
    { label: "问题来源", key: "source", list: problemSource },
    { label: "严重程度", key: "severity", list: severityList },
]
export const otherMineList = [
    { label: "纵队", key: "team", list: columnList },
    { label: "应用层级", key: "appPriority", list: appLayerList },
    { label: "归属代表处/系统部", key: "represent", list: addressList },
]
export const otherList = [
    { label: "严重程度", key: "severity", list: severityList },
    { label: "纵队", key: "team", list: columnList },
    { label: "应用层级", key: "appPriority", list: appLayerList },
    { label: "归属代表处/系统部", key: "represent", list: addressList },
]

export const transLabel2Value = (label, dictList) => {
    for (const item of dictList) {
        if (item.label === label) {
            return item.value;
        }
    }
}

export const transValue2Label = (value, dictList) => {
    for (const item of dictList) {
        if (item.value === value) {
            return item.label;
        }
    }
}
export const phoneStr = [
    'HUAWEI PURA X',
    'HUAWEI PURA X 典藏版',
    'HUAWEI PURA 80 PRO',
    'HUAWEI PURA 80 PRO+',
    'HUAWEI PURA 80 ULTRA',
    'HUAWEI PURA 70',
    'HUAWEI PURA 70 乐臻版',
    'HUAWEI PURA 70 北斗卫星消息版',
    'HUAWEI PURA 70 ULTRA',
    'HUAWEI PURA 70 PRO',
    'HUAWEI PURA 70 PRO+',
    'HUAWEI PURA 70 PRO+ 乐臻版',
    'HUAWEI MATE 70',
    'HUAWEI MATE 70 鸿蒙NEXT先锋版',
    'HUAWEI MATE 70 PRO',
    'HUAWEI MATE 70 PRO 优享版',
    'HUAWEI MATE 70 PRO 鸿蒙NEXT先锋版',
    'HUAWEI MATE 70 PRO 优享版 鸿蒙NEXT先锋版',
    'HUAWEI MATE 70 PRO+',
    'HUAWEI MATE 70 PRO+ 鸿蒙NEXT先锋版',
    'HUAWEI MATE 70 RS',
    'HUAWEI MATE 70 RS  ULTIMATE DESIGN',
    'HUAWEI MATE 60',
    'HUAWEI MATE 60 乐臻版',
    'HUAWEI MATE 60 PRO',
    'HUAWEI MATE 60 PRO 乐臻版',
    'HUAWEI MATE 60 PRO+',
    'HUAWEI MATE 60 RS  ULTIMATE DESIGN',
    'HUAWEI MATE X5',
    'HUAWEI MATE X5 典藏版',
    'HUAWEI MATE X6',
    'HUAWEI MATE X6 鸿蒙NEXT先锋版',
    'HUAWEI MATE X6 典藏版',
    'HUAWEI MATE X6 典藏版 鸿蒙NEXT先锋版',
    'HUAWEI MATE XT  ULTIMATE DESIGN',
    'HUAWEI NOVA 14',
    'HUAWEI NOVA 14 PRO',
    'HUAWEI NOVA 14 ULTRA',
    'HUAWEI NOVA 13',
    'HUAWEI NOVA 13 PRO',
    'HUAWEI NOVA 12',
    'HUAWEI NOVA 12 PRO',
    'HUAWEI NOVA 12 ULTRA',
    'HUAWEI NOVA 12 ULTRA 星耀版',
    'HUAWEI NOVA FLIP',
    'HUAWEI POCKET 2',
    'HUAWEI POCKET 2 艺术定制版',
]
export const padStr = [
    'HUAWEI MATEPAD 11.5S',
    'HUAWEI MATEPAD 11.5S 灵动款',
    'HUAWEI MATEPAD PRO 2023 13.2英寸',
    'HUAWEI MATEPAD PRO 2025 13.2英寸',
    'HUAWEI MATEPAD PRO 柔光版 2025 13.2英寸',
    'HUAWEI MATEPAD PRO 柔光版 鸿蒙NEXT先锋版 2025 13.2英寸',
    'HUAWEI MATEPAD PRO',
    'HUAWEI MATEPAD PRO 2024 11英寸',
    'HUAWEI MATEPAD PRO 星闪版 2024 11英寸',
    'HUAWEI MATEPAD PRO 2024 12.2英寸',
    'HUAWEI MATEPAD PRO  2024 12.2英寸',
    'HUAWEI MATEPAD PRO 流金典藏版 2024 12.2英寸',
    'HUAWEI MATEPAD PRO 柔光版 2024 12.2英寸',
    'HUAWEI MATEPAD AIR 2024 12英寸',
    'HUAWEI MATEPAD AIR 柔光版 2024 12英寸',
]
export const pcStr = [
    'HUAWEI MATEBOOK PRO 2025 14.2英寸',
    'HUAWEI MATEBOOK PRO 柔光版 2025 14.2英寸',
    'HUAWEI MATEBOOK FOLD  ULTIMATE DESIGN 2025 13英寸',
    'HUAWEI 擎云 HM940'
]
export const watchStr = ['WATCH 5']

export const getChildrenList = (str) => {
    return str.map((item => {
        return {
            label: item,
            value: item
        }
    }))
}
export const productMap = {
    手机: getChildrenList(phoneStr),
    平板: getChildrenList(padStr),
    PC: getChildrenList(pcStr),
    手表: getChildrenList(watchStr),
}

// 问题分类
export const classificationOptions = [
    {
        label: '功能缺失',
        value: '功能缺失'
    },
    {
        label: '功能BUG',
        value: '功能BUG'
    },
    {
        label: '稳定性',
        value: '稳定性'
    },
    {
        label: '性能',
        value: '性能'
    },
    {
        label: '功耗',
        value: '功耗'
    },
    {
        label: '兼容性',
        value: '兼容性'
    },
    {
        label: 'UX',
        value: 'UX'
    },
    {
        label: '安全隐私',
        value: '安全隐私'
    },
    {
        label: '其他',
        value: '其他'
    },
    {
        label: '应用缺失',
        value: '应用缺失'
    },
]

// 优先级
export const priorityOption = [
    { label: "高", value: "高" },
    { label: "中", value: "中" },
    { label: "低", value: "低" },
]
export const PRIORTY_DEFAULT_VALUE = "高"
