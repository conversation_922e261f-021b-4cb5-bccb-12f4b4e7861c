import { ewpService } from "@/utils/axios"

export const getOriginChartData = async (data) => {
  const response = await ewpService.post(`/statisitc/getOpinionDashboard`, data)
  return response
}

export const queryTotalVolume = async (data) => {
  const response = await ewpService.post(`/statisitc/queryTotalVolume`, data)
  return response
}
export const queryCompletionRate = async (data) => {
  const response = await ewpService.post(`/statisitc/queryCompletionRate`, data)
  return response
}
export const queryProblemCount = async (data) => {
  const response = await ewpService.post(`/statisitc/queryProblemCount`, data)
  return response
}