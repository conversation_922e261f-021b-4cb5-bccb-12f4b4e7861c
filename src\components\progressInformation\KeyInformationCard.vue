<template>
  <div class="card key-information-card">
    <div class="title-row">
      <span class="title">关键信息</span>
    </div>
    <el-divider />
    <div class="information-container" v-for="item in keyInformation" :key="item.label">
      <div class="label">{{ item.label }}</div>
      <div class="value">
        <span :title="item.value" v-if="item.type === 'link'" class="link">
          <span>{{ item.value }}</span>
          <el-icon class="copy" @click="copyDTS(item.value)"><CopyDocument :size="20"/></el-icon>
        </span>
        <span :title="item.value" v-else-if="item.label==='汇总声量'">
          <span>{{item.value}}</span>
          <a @click="toIssueOrigin(item.sceneId,item.sceneName)" class="origin">查看源声</a>
        </span>
        <span :title="item.value" v-else>{{ item.value || '---' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Back, CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import {defineProps, toRefs} from "vue";
import {useRouter} from "vue-router";
const props = defineProps({
  keyInformation: {
    default: () => {},
    required: true,
  }
});

const router = useRouter();

const {keyInformation} = toRefs(props);

const copyDTS = (DTS) => {
  const copyInput = document.createElement("input");
  copyInput.setAttribute("value", DTS);
  document.body.appendChild(copyInput);
  copyInput.select();
  document.execCommand("copy");
  document.body.removeChild(copyInput);
  ElMessage({
    type: "success",
    message: "复制成功!",
  })
}

const toIssueOrigin = (sceneId,sceneName) => {
  router.push({
    path: "/source-scene-detail",
    query: { sceneId: sceneId,sceneName: sceneName },
  });
}

</script>

<style lang="scss" scoped>
.left-area {
    width: 250px;
    box-sizing: border-box;
    .key-information-card {
      .title-row {
        text-align: left;
        vertical-align: center;
        position: relative;
        .title {
          color: rgb(25, 25, 25);
          font-family: 鸿蒙黑体;
          font-size: 16px;
          font-weight: 700;
          line-height: 28px;
          letter-spacing: 0px;
          text-align: left;
        }
        .icon {
          position: absolute;
          right: 0;
          top: 50%;
          margin-top: -10px;
        }
      }
      .el-divider {
        margin-top: 10px;
        margin-bottom: 30px;
      }
      .information-container {
        height: 100%;
        display: flex;

        .label,
        .value {
          display: inline-block;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-family: HarmonyOS Sans SC;
          font-weight: 400;
          line-height: 22px;
          letter-spacing: 0px;
          text-align: left;
          margin-bottom: 16px;
        }

        .label {
          color: rgb(119, 119, 119);
          font-size: 12px;
          width: 36%;
          flex-shrink: 0;
        }

        .value {
          color: rgb(0, 0, 0);
          font-size: 14px;
          margin-left: 16px;
          flex-grow: 1;
        }

        .link {
          white-space: nowrap;
          span {
            display: inline-block;
            width: calc(100% - 20px);
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
            font-family: HarmonyOS Sans SC;
            font-size: 12px;
            font-weight: 400;
          }
          i.copy {
            vertical-align: middle;
            cursor: pointer;
          }
        }

        .origin {
          float: right;
          color: #0c93f9;
          cursor: pointer;
        }
      }
    }
  }

</style>
