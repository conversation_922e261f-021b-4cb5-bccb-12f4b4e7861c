<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>系统登录</h1>
        <div v-if="isDevMode" class="dev-mode-badge">开发模式</div>
      </div>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="top"
      >
        <el-form-item prop="account" label="用户名">
          <el-input v-model="formData.account" placeholder="请输入用户名">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password" label="密码">
          <el-input
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="formData.rememberMe">记住我</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleLogin"
            style="width: 100%"
          >
            登录
          </el-button>
        </el-form-item>
        <el-form-item v-if="isDevMode">
          <el-button type="success" @click="handleDevLogin" style="width: 100%">
            开发模式快速登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { User, Lock } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
import type { FormInstance } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { isDevMode } from "@/utils/env";

const userStore = useUserStore();
const formRef = ref<FormInstance | null>(null);
const loading = ref(false);
const REMEMBER_KEY = "loginInfo";
const formData = reactive({
  account: "",
  password: "123456",
  rememberMe: false,
});
const router = useRouter();
const route = useRoute();
const rules = {
  account: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
};

onMounted(() => {
  const loginInfo = localStorage.getItem(REMEMBER_KEY);
  if (loginInfo) {
    const { account, password, remember } = JSON.parse(loginInfo);
    formData.account = account;
    formData.password = password;
    formData.rememberMe = remember;
  }
});

// 开发模式快速登录
const handleDevLogin = async () => {
  if (isDevMode) {
    loading.value = true;
    try {
      // 在开发模式下设置用户信息
      userStore.setUserInfo({
        account: formData.account,
        cnNameEmpNo: formData.account,
        enNameEmpNo: formData.account,
        userId: "dev_001",
      });

      // 初始化其他用户数据
      await userStore.getUserRoleList();
      await userStore.getUserPermissions();

      // 跳转到首页
      router.replace("/");
    } catch (error) {
      console.error("开发模式登录失败:", error);
    } finally {
      loading.value = false;
    }
  }
};

const handleLogin = () => {
  if (!formRef.value) return;
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        const { account, password } = formData;
        const params = {
          account: account,
          password,
        };
        const { status } = await userStore.login(params);
        if (status === "200") {
          if (formData.rememberMe) {
            localStorage.setItem(
              REMEMBER_KEY,
              JSON.stringify({
                account,
                password,
                remember: formData.rememberMe,
              })
            );
          } else {
            localStorage.removeItem(REMEMBER_KEY);
          }
          const toPath = decodeURIComponent(
            (route.query?.redirect || "/") as string
          );
          console.log("toPath:", toPath);
          console.log("route.name :>> ", route.name);
          if (route.name === "Login") {
            router.replace("/");
          } else {
            router.replace(toPath);
          }
        }
      } catch (error) {
        console.error("登录失败:", error);
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;

  .login-card {
    width: 400px;
    padding: 40px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .login-header {
      text-align: center;
      margin-bottom: 30px;
      position: relative;

      h1 {
        font-size: 24px;
        color: #333;
      }

      .dev-mode-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #67c23a;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}
</style>
