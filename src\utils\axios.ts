import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { ElMessage } from 'element-plus';
import { isDevMode, isProdMode, mock, UNAUTHORIZED_PAGE, WO_AUTH } from './env';
import { storage } from './Storage';
import { csrfTokenManager } from './CSRFTokenManager';

export const ewpService: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || '',
  timeout: 50000,
  withCredentials: false,
});

let isFetchingToken = false;
const getCsrfToken = async () => {
  if (isFetchingToken) {
    return;
  }
  isFetchingToken = true;
  try {
    const response = await ewpService.get('/user/getCsrfToken');
    if(response){
      const token = response.token;
      const expiration = response.expiration;
      const effectiveTime = response.effectiveTime;

      csrfTokenManager.updateTokenData({
        token,
        expiration,
        effectiveTime
      });
    }
  } finally {
    isFetchingToken = false;
  }
};

export const clearCsrfToken = () => {
  csrfTokenManager.clearToken();

  storage.removeCookie('csrfToken');
  storage.removeCookie('csrfTokenExpiration');
  storage.removeCookie('csrfTokenEffectiveTime');
};

// 请求拦截器
ewpService.interceptors.request.use(
  async (config) => {
    if (!isProdMode() && !mock) {
      config.headers.token = atob(WO_AUTH)
    }

    const currentToken = csrfTokenManager.getToken();

    if (currentToken && !csrfTokenManager.isTokenExpired()) {
      config.headers['X-CSRF-TOKEN'] = currentToken;
    } else {
      await getCsrfToken();
      const newToken = csrfTokenManager.getToken();
      if (newToken) {
        config.headers['X-CSRF-TOKEN'] = newToken;
      }
    }

    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
ewpService.interceptors.response.use(
  async (response: AxiosResponse) => {
    const { data } = response;
    if (data.code === 200) {
      return data.data;
    }

    if (data.code === 40007) {
      ElMessage.error('token已过期，请重新登录');
      clearCsrfToken();
      window.location.href = UNAUTHORIZED_PAGE
      return Promise.reject(data);
    }
    // 临时解决
    if (data.code === 40019) {
      clearCsrfToken();
      await getCsrfToken();
      return Promise.reject(data);
    }
    return Promise.reject(data);
  },
  (error: AxiosError) => {
    const status = error.response?.status;
    switch (status) {
      case 400:
        ElMessage.error('请求参数错误');
        break;
      case 404:
        ElMessage.error('请求地址不存在');
        break;
      case 500:
        ElMessage.error('服务器内部错误');
        break;
      case 401:
        ElMessage.error('token已过期，请重新登录');
        clearCsrfToken();
        break;
      case 413:
        ElMessage.error('导出数据太大，限制50000条');
        break;
      default:
        ElMessage.error(error.message || '网络异常');
    }
    return Promise.reject(error);
  }
);

