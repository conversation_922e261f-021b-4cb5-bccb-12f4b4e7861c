/* 表格 */
.el-table {
  --el-table-header-text-color: var(--default-color);
  --el-table-header-bg-color: #fafafa;
  --el-table-border-color: #e2e7ec;
}

.el-table .el-table__header .cell {
  border-right: 1px solid rgb(196, 196, 196);
  color: var(--default-color);
  font-weight: 700;
}

.el-table .el-table__header tr th:last-child .cell {
  border-right: unset;
}

/* 气泡样式 */
.popper-class {
  font-size: 14px;
  color: var(--default-color);
  /* transition: all 1s linear; */
}

/* 分页 */
.el-pagination {
  /* 圆角 */
  --el-pagination-border-radius: 8px;
  /* 页签数字 背景色 */
  --el-pagination-button-bg-color: var(--transparent-color);
  /* pre、next 箭头背景色 */
  --el-disabled-bg-color: var(--transparent-color);
  /* 不可点击箭头颜色 */
  --el-text-color-placeholder: rgb(201, 201, 201);
  /* 文本颜色 */
  --el-text-color-primary: var(--default-color);
}
.pagination-total {
  color: #777777;
  font-size: 14px;
}
