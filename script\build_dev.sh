#!/bin/bash
# ***********************************************************************
# Copyright: (c) Huawei Technologies Co., Ltd. 2021. All rights reserved.
# script for build
# version: 1.0.0
# change log:
# ***********************************************************************
set -eux
set -o pipefail

BASE_PATH=$(cd $(dirname $0);pwd)
PROJECT_PATH=$(cd $BASE_PATH/..;pwd)

SERVICE_NAME=$(awk -F "=" '/service_name/ {print $2}' "${BASE_PATH}"/service.ini)

cd $PROJECT_PATH
PACKAGE_VERSION=$(npm pkg get version|tr -d '"')
PACKAGE_VERSION=${PACKAGE_VERSION}.${ENV_PIPELINE_STARTTIME}

function format_file() {
  cd "${PROJECT_PATH}"
  find . -type f | grep -E '\.xml$|\.yaml$|\.sh$|\.yml$|\.txt$|\.properties$|\.tf$' | xargs dos2unix
}

function build_app_package() {
  cd "${PROJECT_PATH}"
#  *************:8882
  sed -i "s/************:8882/*************:8084/g" .env.production
  pnpm install
  pnpm run build
  cd script
  sed -i "s/\#VERSION/${PACKAGE_VERSION}/g" package.json
  cd "${PROJECT_PATH}"
  mkdir -p tmpService
  rm -rf dist/CNAME
#  sed -i "s#\/assets#\/buzz3\/partnervocmngportal\/environment-rnd\/assets#g" dist/index.html
  cp -r dist/ tmpService/
  cp script/package.json tmpService/
  cd tmpService
  zip -r service.zip *
  cp ./service.zip "${WORKSPACE}"
}

function build_app_package_bak() {
  cd "${PROJECT_PATH}"
  cd script
  sed -i "s/\#VERSION/${PACKAGE_VERSION}/g" package.json
  cd "${PROJECT_PATH}"
  mkdir -p tmpService
#  sed -i "s#\/assets#\/buzz3\/partnervocmngportal\/environment-rnd\/assets#g" dist/index.html
  cp -r script/dist tmpService/
  cp script/index.html tmpService/dist/
  cp script/package.json tmpService/
  cd tmpService
  zip -r service.zip *
  cp ./service.zip "${WORKSPACE}"
}


function iacPackage(){
  cd ${PROJECT_PATH}
  cd iac
  sed -i "s/\#VERSION/${PACKAGE_VERSION}/g" package.json
  sed -i "s/\#VERSION/${PACKAGE_VERSION}/g" global/PartnerVOCMngPortal/resources.yaml
  sed -i "s/\#ENV/environment-rnd-dev/g" global/PartnerVOCMngPortal/resources.yaml
  zip -r iac.zip *
  cp ./iac.zip "${WORKSPACE}"
}

function combinePackage() {
  cd "${BASE_PATH}"
  bash ./combinePackage.sh "${SERVICE_NAME}" "${PACKAGE_VERSION}"
}

function upload_package() {
  cd ${WORKSPACE}
  # zip -r ${PackageName} *.zip package.json
  bash ${WORKSPACE}/Script/clouddragon/build2.0/service/getPackageInfo.sh "" "*.zip"
}

function main() {
  format_file
  build_app_package
  iacPackage
  upload_package
  #打组合包
#  combinePackage
  buildVersion=${PACKAGE_VERSION}
  echo buildVersion="${buildVersion}" >> ${WORKSPACE}/buildInfo.properties
}

main
