/**
 * CSRF Token 管理器
 * 防跨站请求伪造标识（CSRFToken）禁止从URL或Cookie中存取，
 * 应存储在页面表单隐藏字段或自定义HTTP头部中
 */
export class CSRFTokenManager {
  private static instance: CSRFTokenManager;
  private token: string | null = null;
  private expiration: number | null = null;
  private effectiveTime: number | null = null;
  private hiddenInput: HTMLInputElement | null = null;

  private constructor() {
    this.initializeHiddenInput();
    this.loadTokenFromMeta();
  }

  public static getInstance(): CSRFTokenManager {
    if (!CSRFTokenManager.instance) {
      CSRFTokenManager.instance = new CSRFTokenManager();
    }
    return CSRFTokenManager.instance;
  }


  private initializeHiddenInput(): void {
    this.hiddenInput = document.querySelector('input[name="csrf-token"]') as HTMLInputElement;
    
    if (!this.hiddenInput) {
      this.hiddenInput = document.createElement('input');
      this.hiddenInput.type = 'hidden';
      this.hiddenInput.name = 'csrf-token';
      this.hiddenInput.id = 'csrf-token-field';
      
      document.body.appendChild(this.hiddenInput);
    }
  }


  private loadTokenFromMeta(): void {
    const metaToken = document.querySelector<HTMLMetaElement>('meta[name="csrf-token"]')?.content;
    if (metaToken && metaToken !== '{{ .csrfToken }}') {
      this.setToken(metaToken);
    }
  }


  public setToken(token: string, expiration?: number, effectiveTime?: number): void {
    this.token = token;
    this.expiration = expiration || null;
    this.effectiveTime = effectiveTime || null;
    
    if (this.hiddenInput) {
      this.hiddenInput.value = token;
    }
    
    const tokenData = {
      token,
      expiration: this.expiration,
      effectiveTime: this.effectiveTime,
      timestamp: Date.now()
    };
    sessionStorage.setItem('csrf-token-data', JSON.stringify(tokenData));
  }


  public getToken(): string | null {
    if (this.hiddenInput && this.hiddenInput.value) {
      return this.hiddenInput.value;
    }
    
    if (this.token) {
      return this.token;
    }
    
    // 从 sessionStorage 获取（作为备份）
      const tokenData = sessionStorage.getItem('csrf-token-data');
      if (tokenData) {
        const parsed = JSON.parse(tokenData);
        this.token = parsed.token;
        this.expiration = parsed.expiration;
        this.effectiveTime = parsed.effectiveTime;
        
        // 恢复到隐藏字段
        if (this.hiddenInput && this.token) {
          this.hiddenInput.value = this.token;
        }
        
        return this.token;
      }
    } catch (error) {
      console.warn('Failed to parse CSRF token from sessionStorage:', error);
    }
    
    return null;
  }

  /**
   * 检查 Token 是否过期
   */
  public isTokenExpired(): boolean {
    if (!this.expiration) {
      return false; // 如果没有过期时间，认为未过期
    }
    return Date.now() > this.expiration;
  }

  /**
   * 获取过期时间
   */
  public getExpiration(): number | null {
    return this.expiration;
  }

  /**
   * 获取生效时间
   */
  public getEffectiveTime(): number | null {
    return this.effectiveTime;
  }

  /**
   * 清除 CSRF Token
   */
  public clearToken(): void {
    this.token = null;
    this.expiration = null;
    this.effectiveTime = null;
    
    // 清除隐藏字段
    if (this.hiddenInput) {
      this.hiddenInput.value = '';
    }
    
    // 清除 sessionStorage
    sessionStorage.removeItem('csrf-token-data');
  }

  /**
   * 更新 Token 数据（用于从服务器响应更新）
   */
  public updateTokenData(data: { token: string; expiration: number; effectiveTime: number }): void {
    this.setToken(data.token, data.expiration, data.effectiveTime);
  }
}

// 导出单例实例
export const csrfTokenManager = CSRFTokenManager.getInstance();
