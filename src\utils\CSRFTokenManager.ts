export class CSRFTokenManager {
  private static instance: CSRFTokenManager;
  private token: string | null = null;
  private expiration: number | null = null;
  private effectiveTime: number | null = null;
  private hiddenInput: HTMLInputElement | null = null;
  private refreshTimer: number | null = null;
  private refreshInterval: number = 5 * 60 * 1000; // 默认5分钟刷新一次

  private constructor() {
    this.initializeHiddenInput();
    this.loadTokenFromMeta();
  }

  public static getInstance(): CSRFTokenManager {
    if (!CSRFTokenManager.instance) {
      CSRFTokenManager.instance = new CSRFTokenManager();
    }
    return CSRFTokenManager.instance;
  }

  private initializeHiddenInput(): void {
    this.hiddenInput = document.querySelector('input[name="csrf-token"]') as HTMLInputElement;
    
    if (!this.hiddenInput) {
      this.hiddenInput = document.createElement('input');
      this.hiddenInput.type = 'hidden';
      this.hiddenInput.name = 'csrf-token';
      this.hiddenInput.id = 'csrf-token-field';
      
      document.body.appendChild(this.hiddenInput);
    }
  }

  private loadTokenFromMeta(): void {
    const metaToken = document.querySelector<HTMLMetaElement>('meta[name="csrf-token"]')?.content;
    if (metaToken && metaToken !== '{{ .csrfToken }}') {
      this.setToken(metaToken);
    }
  }

  public setToken(token: string, expiration?: number, effectiveTime?: number): void {
    this.token = token;
    this.expiration = expiration || null;
    this.effectiveTime = effectiveTime || null;

    if (this.hiddenInput) {
      this.hiddenInput.value = token;
    }
  }

  public getToken(): string | null {
    if (this.hiddenInput && this.hiddenInput.value) {
      return this.hiddenInput.value;
    }
    
    if (this.token) {
      return this.token;
    }

    return null;
  }


  public isTokenExpired(): boolean {
    if (!this.expiration) {
      return false;
    }
    return Date.now() > this.expiration;
  }


  public getExpiration(): number | null {
    return this.expiration;
  }


  public getEffectiveTime(): number | null {
    return this.effectiveTime;
  }


  public clearToken(): void {
    this.token = null;
    this.expiration = null;
    this.effectiveTime = null;

    // 清除隐藏字段
    if (this.hiddenInput) {
      this.hiddenInput.value = '';
    }
  }


  public updateTokenData(data: { token: string; expiration: number; effectiveTime: number }): void {
    this.setToken(data.token, data.expiration, data.effectiveTime);
  }

  public startAutoRefresh(intervalMs?: number, refreshCallback?: () => Promise<void>): void {
    if (intervalMs) {
      this.refreshInterval = intervalMs;
    }

    this.stopAutoRefresh();

    this.refreshTimer = window.setInterval(async () => {
      try {
        if (refreshCallback) {
          await refreshCallback();
        }
      } catch (error) {
        console.warn('Auto refresh CSRF token failed:', error);
      }
    }, this.refreshInterval);
  }

  public stopAutoRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  public setRefreshInterval(intervalMs: number): void {
    this.refreshInterval = intervalMs;
    if (this.refreshTimer) {
      this.stopAutoRefresh();
    }
  }
}

export const csrfTokenManager = CSRFTokenManager.getInstance();
