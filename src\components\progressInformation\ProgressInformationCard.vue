<template>
  <div class="card progress-information-card">
    <div class="title">进展信息</div>
    <el-divider />
    <div class="progress-container">
      <div class="progress" v-for="(item,index) in progressData" :key="item.title">
        <div class="title" :class="{ current: item.isCurrent  }">{{ item.title }}</div>
        <div class="operation-container">
          <div class="operation" @click="selectOperation(node)" :class="{ select: node.isSelected}" v-for="(node,subIndex) in item.operations" :key="node.operation">
            <div class="node" :class="{ finished: node.isFinished && !node.reject && !node.beRejected && !isYellow, reject: node.reject || (node.step==='0000' && isYellow && node.isCurrent), 'be-rejected': node.beRejected && !isYellow }">{{ `${operationIndex(index,subIndex)} ${node.operation}` }}</div>
            <div class="operator" v-if="node.operator">
              <el-icon :size="10"><UserFilled /></el-icon>
              <span>{{ node.operator }}</span>
            </div>
          </div>
        </div>
        <div class="duration-container">
          <el-icon :size="16" class="icon-clock"><AlarmClock /></el-icon>
          <span class="duration-label">{{ `停留时长` }}</span>
          <span class="duration">{{ `${item.duration ?? '/'}天` }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { UserFilled, AlarmClock } from '@element-plus/icons-vue'
import {computed, defineProps, onBeforeMount, onMounted, ref, toRefs, watch} from "vue";
import {useEventBus} from "@/hooks/useEventBus";

const emit = defineEmits(["update:updateProgress"]);

const props = defineProps({
  progressData: {
    default: () => {},
    required: true,
  }
});
const { progressData } = toRefs(props);

const isYellow = ref(false);

const eventBus =  useEventBus();

const selectOperation = node =>{
  if(node.isFinished || node.isCurrent || node.reject || node.beRejected){
    //将isSelected重置为false;
    props.progressData.forEach((item, index) => {
      item.operations.forEach((subItem) => {
        subItem.isSelected = false;
      })
    })
    node.isSelected = true;
    emit('update:updateProgress', progressData);
  }
}

const arrLength = computed(() => {
  const arr = [];
  props.progressData.forEach((item, index) => {
    arr.push(item.operations.length);
  });
  return arr;
})

const operationIndex = (index,subIndex)=>{
  if (index > 0 ){
    let total = 0;
    for (let i = 0; i < index; i++) {
      total += arrLength.value[i];
    }
    total += subIndex+1;
    return total;
  }
  return subIndex+1;
}

onMounted(()=>{
  eventBus.on('updateReplicateColor',flag =>{
    isYellow.value = flag;
  })
})

onBeforeMount(()=>{
  eventBus.off('updateReplicateColor')
})

</script>

<style lang="scss" scoped>
.progress-information-card {
  min-width: 823px;
  & > .title {
    color: rgba(0, 0, 0, 0.9);
    font-family: 微软雅黑;
    font-size: 16px;
    font-weight: 700;
    line-height: 40px;
    letter-spacing: 0%;
    text-align: left;
  }
  .el-divider {
    margin-top: 0;
    margin-bottom: 16px;
  }
  .progress-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  .progress {
    margin-left: 10px;
    flex: 1;
    &:first-child {
      margin-left: 0 !important;
    }
    .title {
      // width: 292px;
      height: 50px;
      background-color: #E6EEFE;
      position: relative;
      line-height: 50px;
      color: rgb(25, 25, 25);
      font-family: 鸿蒙黑体;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 0px;
      text-align: center;

      &::before {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-left: 10px solid #FFFFFF;
        border-top: 25px solid #E6EEFE;
        border-bottom: 25px solid #E6EEFE;
      }
      &::after {
        content: '';
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        width: 0;
        height: 0;
        border-left: 10px solid #E6EEFE;
        border-top: 25px solid #FFFFFF;
        border-bottom: 25px solid #FFFFFF;
      }

      &.current {
        background-color: #58A1FC;
        color: rgb(255, 255, 255);
        border-radius: 8px 0 0 8px;
        &::before {
          border-top: 25px solid #58A1FC;
          border-bottom: 25px solid #58A1FC;
        }
        &::after {
          border-left: 10px solid #58A1FC;
        }
      }
    }

    .operation-container {
      width: calc(100% - 8px);
      border-radius: 8px 8px 0 0;
      background: rgb(248, 248, 248);
      margin-top: 6px;
      height: 100px;
      padding-top: 8px;
      .operation {
        padding-left: 36px;
        display: block;
        text-align: left;
        margin-top: 8px;
        &.current {
          background: rgba(10, 89, 247, 0.1);
        }
        &.select {
          background: rgba(10, 89, 247, 0.1);
        }
        .node {
          position: relative;
          font-family: 鸿蒙黑体;
          font-size: 14px;
          font-weight: 400;
          line-height: 150%;
          letter-spacing: 0%;
          &::before {
            display: block;
            content: '';
            position: absolute;
            top: 50%;
            left: -20px;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
          }
          &.finished::before {
            background: #65bb5b;
          }
          &.reject::before {
            background: #fa9841;
          }
          &.be-rejected::before {
            background: #d64a52;
          }
        }
        .operator {
          color: rgba(0, 0, 0, 0.6);
          font-family: 鸿蒙黑体;
          font-size: 10px;
          font-weight: 400;
          line-height: 150%;
          letter-spacing: 0%;
          text-align: left;
          margin-bottom: -13px;
          span,
          i {
            vertical-align: middle;
          }
          span {
            display: inline-block;
            margin-left: 8px;
          }
        }
        &:nth-child(n + 2) {
          margin-top: 18px;
        }
      }
    }

    .duration-container {
      width: calc(100% - 8px);
      border-radius: 0px 0px 8px 8px;
      background: #F0F0F0;
      height: 42px;
      display: flex;
      align-items: center;
      .icon-clock {
        margin-left: 16px;
      }

      .duration-label {
        color: rgb(0, 0, 0);
        font-family: 鸿蒙黑体;
        font-size: 14px;
        font-weight: 400;
        line-height: 150%;
        letter-spacing: 0%;
        text-align: left;
        margin-left: 11px;
      }

      .duration {
        color: rgba(0, 0, 0, 0.6);
        font-family: 鸿蒙黑体;
        font-size: 14px;
        font-weight: 400;
        line-height: 150%;
        letter-spacing: 0%;
        text-align: center;
        margin-left: 8px;
      }
    }

    &:first-child {
      .title {
        &::before {
          display: none;
        }
      }
      .operation-container,
      .duration-container {
        margin-left: 0;
      }
    }

    &:last-child {
      .title {
        border-radius: 0 8px 8px 0;
        &::after {
          display: none;
        }
      }
      .operation-container,
      .duration-container {
        width: 100%;
      }
    }
    }
  }
}

</style>
