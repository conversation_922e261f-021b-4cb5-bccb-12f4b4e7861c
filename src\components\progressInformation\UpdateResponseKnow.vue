<template>
  <div class="card relate-order-card" style="margin-top: 20px">
    <div class="title">更新答复口径</div>
    <div class="description">进展信息</div>
    <el-form
      :model="progressData"
      class="form"
      style="margin-top: 20px; display: flex; align-items: center"
      label-width="95"
      label-position="top"
      :inline="true"
      :disabled="!canEdit"
      :rules="progressRules"
      ref="progressRef"
    >
      <el-form-item
        label="更新类型"
        :label-position="'top'"
        class="form-item"
        prop="updateType"
      >
        <el-radio-group v-model="progressData.updateType" @change="changeUpdateType">
          <el-radio :value="updateTypeOptions[0].value">{{
            updateTypeOptions[0].label
          }}</el-radio>
          <el-radio :value="updateTypeOptions[1].value">{{
            updateTypeOptions[1].label
          }}</el-radio>
          <el-radio :value="updateTypeOptions[2].value">{{
            updateTypeOptions[2].label
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="答复口径"
        :label-position="'top'"
        class="form-item"
        prop="weknowId"
      >
        <el-input
          type="input"
          :disabled="
            progressData.updateType === '不涉及' ||
            progressData.updateType === '下架'
          "
          placeholder="请输入服务知识Id"
          v-model="progressData.weknowId"
        />
      </el-form-item>
    </el-form>
    <div class="description" v-if="!isNodeFinished && canEdit">操作信息</div>
    <el-form
      ref="formRef"
      :inline="true"
      v-if="!isNodeFinished && canEdit"
      :model="formData"
      :rules="rulesAssignment"
      label-width="95"
      label-position="top"
      style="margin-top: 20px; display: flex; align-items: center"
    >
      <el-form-item
        label="操作类型"
        class="form-item"
        prop="operationType"
        style="margin-right: 20px"
      >
        <el-radio-group
          v-model="formData.operationType"
          @change="changeOperationType"
        >
          <el-radio :value="operationOptions[1].value">{{
            operationOptions[1].label
          }}</el-radio>
          <el-radio :value="operationOptions[0].value">{{
            operationOptions[0].label
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="指定处理人"
        class="form-item"
        style="margin-left: 24px; width: 20%"
        prop="nextHandler"
        v-if="formData.operationType === '0000'"
      >
        <el-select v-model="formData.nextHandler">
          <el-option
            v-for="item in handlerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="canEdit" class="operate-button-container text-center">
      <el-button id="save-button" @click="onSave">{{
        isNodeFinished ? "更新信息" : "保存"
      }}</el-button>
      <el-button
        v-if="!isNodeFinished"
        id="assign-button"
        type="primary"
        @click="onSubmit"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, toRefs } from "vue";
import { useRoute } from "vue-router";
import { ElLoading, ElMessage, type FormInstance } from "element-plus";
import { operationTypeOptions as operationOptions } from "@/business/progressInformation/delimitProblem";
import { rulesAssignment } from "@/business/progressInformation/replicateProblem";
import { getHandlers } from "@/business/progressInformation/common/getHandlers";
import { weKnowIDReg } from "@/business/progressInformation/common/regex";
import { ewpService } from "@/utils/axios";
import { useFlowInfoStore } from "@/store";
import { cloneDeep, isEqual } from "lodash-es";

const emit = defineEmits(["refresh"]);

const flowInfoStore = useFlowInfoStore();

const refreshParentData = (step = null) => {
  emit("refresh", step);
};
const props = defineProps({
  node: {
    default: () => {},
    required: true,
  },
  canEdit: {
    default: () => false,
    required: true,
  },
});

const updateTypeOptions = [
  { label: "更新", value: "更新" },
  { label: "下架", value: "下架" },
  { label: "不涉及", value: "不涉及" },
];

const progressRef = ref<FormInstance>();

const formRef = ref<FormInstance>();

const progressRules = {
  updateType: [{ required: true, message: "更新类型必填！" }],
};

const { node } = toRefs(props);
const isNodeFinished = node.value.isFinished;

const route = useRoute();
const opinionIssueId = route.query.id;

const handlerOptions = ref([]);

const progressData = reactive({
  weknowId: "",
  updateType: "",
});

const formData = reactive({
  operationType: "",
  nextHandler: "",
});

const changeOperationType = async (operationType) => {
  if (operationType === "0001") {
    return;
  }
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let handlerList = null;
  if (opinionIssueId) {
    handlerList = await getHandlers(opinionIssueId, operationType);
  }
  if (handlerList) {
    handlerOptions.value = handlerList.handlers;
    formData.nextHandler = handlerList.defaultHandler;
  } else {
    ElMessage({
      type: "error",
      message: "获取下一步处理人失败",
    });
  }
  loading.close();
};

onMounted(() => {
  fetchExistingData();
});

const onSubmit = async () => {
  if (!formRef.value) return;
  if (!progressRef.value) return;
  if (!checkWeknowId()) return;
  if (formData.operationType === "0002") {
    try {
      const requestData = {
        opinionIssueId: opinionIssueId,
        weKnowId: progressData.weknowId,
        assignedPerson: flowInfoStore.getLastPerson(6),
        updateType: progressData.updateType,
        operateType: formData.operationType,
      };

      await ewpService.post("/standardReplyNine/submit", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "驳回成功",
      });
      refreshParentData();
    } catch (err) {
      ElMessage({
        type: "error",
        message: "驳回失败",
      });
    }
  } else if (formData.operationType === "0000") {
    formRef.value.validate(async (valid2, fields2) => {
      try {
        if (valid2) {
          const requestData = {
            opinionIssueId: opinionIssueId,
            operateType: formData.operationType,
            assignedPerson: formData.nextHandler,
            updateType: progressData.updateType,
            weKnowId: progressData.weknowId,
          };

          await ewpService.post("/standardReplyNine/submit", requestData, {
            headers: {
              "Content-Type": "application/json",
            },
          });
          ElMessage({
            type: "success",
            message: "转单成功",
          });
          refreshParentData();
        }
      } catch (err) {
        ElMessage({
          type: "error",
          message: "转单失败",
        });
      }
    });
  } else {
    progressRef.value.validate(async (valid, fields) => {
      formRef.value.validate(async (valid2, fields2) => {
        try {
          if (valid && valid2) {
            const requestData = {
              opinionIssueId: opinionIssueId,
              operateType: formData.operationType,
              assignedPerson: formData.nextHandler,
              weKnowId: progressData.weknowId,
              updateType: progressData.updateType,
            };

            await ewpService.post("/standardReplyNine/submit", requestData, {
              headers: {
                "Content-Type": "application/json",
              },
            });
            ElMessage({
              type: "success",
              message: "提交成功",
            });
            refreshParentData();
          }
        } catch (err) {
          ElMessage({
            type: "error",
            message: "提交失败",
          });
        }
      });
    });
  }
};

const checkWeknowId = () => {
  if (progressData.updateType !== updateTypeOptions[0].value) {
    return true;
  }
  if (!weKnowIDReg.test(progressData.weknowId)) {
    ElMessage({
      type: "error",
      message: "请输入正确的知识ID",
    });
    return false;
  }
  return true;
};

const onSave = async () => {
  if (!checkWeknowId()) return;
  if (isNodeFinished) {
    try {
      const requestData = {
        opinionIssueId: opinionIssueId,
        operateType: "0001",
        assignedPerson: formData.nextHandler,
        weKnowId: progressData.weknowId,
        updateType: progressData.updateType,
      };
      if (isEqual({ ...progressData }, originData.value)) {
        ElMessage({
          type: "error",
          message: "内容未修改,请修改内容后更新",
        });
        return;
      }

      await ewpService.post("/standardReplyNine/submit", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "更改成功",
      });
      refreshParentData(node.value.step);
    } catch (err) {
      ElMessage({
        type: "error",
        message: "更改失败",
      });
    }
  } else {
    try {
      const requestData = {
        weKnowId: progressData.weknowId,
        opinionIssueId: opinionIssueId,
        operateType: formData.operationType,
        assignedPerson: formData.nextHandler,
        updateType: progressData.updateType,
      };

      await ewpService.post("/standardReplyNine/save", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "保存成功",
      });
      refreshParentData(node.value.step);
    } catch (err) {
      ElMessage({
        type: "error",
        message: "保存失败",
      });
    }
  }
};

const originData = ref({});

// 获取已有数据
const fetchExistingData = async () => {
  if (!opinionIssueId) return;

  try {
    const response = await ewpService.get(
      `/standardReplyNine/queryMaxVersionData?opinionIssueId=${opinionIssueId}`
    );
    if (response) {
      formData.operationType = response.operateType || "";
      formData.nextHandler = response.assignedPerson || "";
      progressData.weknowId = response.weKnowId;
      progressData.updateType = response.updateType;
    }
    originData.value = cloneDeep({ ...progressData });
  } catch (error) {
    ElMessage({
      type: "error",
      message: "查询失败",
    });
  }
};

const changeUpdateType = updateType => {
  if(updateType === '不涉及'||updateType==='下架'){
    progressData.weknowId = '';
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/common-styles.scss";
</style>
