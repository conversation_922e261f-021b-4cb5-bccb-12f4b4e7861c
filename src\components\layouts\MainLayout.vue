<template>
  <div class="main-layout">
    <BaseHeader />
    <div class="main-container">
      <div class="content-container">
        <el-scrollbar @scroll="scroll">
          <div class="content-wrapper">
            <router-view />
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseHeader from "./BaseHeader.vue";
import { useEventBus } from "@/hooks/useEventBus";
const eventBus = useEventBus();
// 监听滚动
const scroll = ({ scrollTop }) => {
  const anchorBox = document.querySelector(".el-anchor");
  // 只监听锚点页面
  if (anchorBox) {
    eventBus.emit("fixedAnchorPoint", scrollTop);
  }
};
</script>

<style scoped lang="scss">
.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .main-container {
    display: flex;
    flex: 1;
    overflow: hidden;

    .content-container {
      flex: 1;
      overflow: hidden;
      background-color: #f1f3f5;

      .content-wrapper {
        padding: 24px 32px;
        min-height: 100%;
        border-radius: 4px;
      }
    }
  }
}

:deep(.dark) {
  .content-wrapper {
    background-color: #1f1f1f !important;
  }
}
</style>
