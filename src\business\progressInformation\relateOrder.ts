export const tableColumns = [
  {
    label: '关联单类型',
    key: 'relatedOrderType'
  },
  {
    label: '关联单号',
    key: 'relatedOrderId'
  },
  // {
  //   label: '关联单状态',
  //   key: 'orderStatus'
  // }
]

export const orderTypeOptions = [
  {
    label: 'DTS单',
    value: 'DTS'
  },
  {
    label: 'IR单',
    value: 'IR'
  },
  {
    label: 'iOne单',
    value: 'IONE'
  },
  {
    label: 'TALM单',
    value: 'TALM'
  }
]

export const rules = {
  classification: [
    { required: true }
  ],
  problemBelongings: [
    { required: true }
  ],
  dilimitProcess: [
    { required: true }
  ],
  conclusion: [
    { required: true }
  ],
  modifySuggestion: [
    { required: true }
  ],
  operationType: [
    { required: true }
  ],
  nextHandler: [
    { required: true }
  ],
}

export const relateRules = {
  relatedOrderType: [
    { required: true }
  ],
  relatedOrderId: [
    { required: true }
  ],
}

// 保存
export const saveRelateOrder = async ({ opinionIssueId, relateWorkOrders }) => {
  const params = {
    opinionIssueId,
    relateWorkOrders
  }
  try {
    const response = await service.put('/relateWorkOrder/save', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response;
  } catch (error) {
      throw error;
  }
}

// 完成/转单
export const submitRelateOrder = async ({ opinionIssueId, nextHandler, operationType, relateWorkOrders }) => {
  const params = {
    opinionIssueId,
    nextHandler,
    operationType,
    relateWorkOrders
  }
  try {
    await service.put('/relateWorkOrder/submit', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
  } catch (error) {
      throw error;
  }
}

// 获取表格数据
export const fetchOrderTable = async (opinionIssueId) => {
  try {
    const response = await service.get(`/relateWorkOrder/view?opinionIssueId=${opinionIssueId}`, {
        headers: {
            'Content-Type': 'application/json',
        },
    }); 
    return response;
  } catch (error) {
      throw error;
  }
}

import { ewpService as service } from '@/utils/axios';
