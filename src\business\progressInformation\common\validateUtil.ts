import {ElMessage} from "element-plus";
import {weKnowIDReg} from "@/business/progressInformation/common/regex";

export const getValidateFun = field =>{
  return (rule, value,callback) => {
    if (value.trim() === '') {
      callback && callback(new Error(field+"不能为空！"))
      return false;
    }
    callback && callback();
    return true;
  };
}

export const validateTable = (value) => {
  return value.trim() !== ''
}
