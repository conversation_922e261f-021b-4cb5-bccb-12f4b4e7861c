export const progressData = [
  {
    title: '问题识别',
    status: '0000',
    operations: [
      {
        operation: '复现问题',
        componentName: 'ReplicateProblemCard',
        isCurrent: false,
        isSelected: false,
        isFinished: false,
        data: {},
        step: '0000'
      }
    ],
    duration: '/'
  },
  {
    title: '问题定界',
    isCurrent: false,
    status: '0001',
    operations: [
      {
        operation: '定界问题',
        componentName: 'DelimitProblemCard',
        isCurrent: false,
        isSelected: false,
        isFinished: false,
        data: {},
        step: '0001',
      },
      {
        operation: '审核定界结论',
        componentName: 'ConfirmDelimit',
        isCurrent: false,
        isSelected: false,
        isFinished: false,
        data: {},
        step: '0002',
      }
    ],
    duration: '/'
  },
  {
    title: '计划锁定',
    status: '0002',
    operations: [
      {
        operation: '锁定解决计划',
        componentName: 'LockSolutionPlan',
        isCurrent: false,
        isSelected: false,
        isFinished: false,
        data: {},
        step: '0004',
      }
    ],
    duration: '/'
  },
  {
    title: '问题修复',
    status: '0003',
    operations: [
      {
        operation: '确认问题修复版本',
        componentName: 'ConfirmFixVersion',
        isCurrent: false,
        isSelected: false,
        isFinished: false,
        data: {},
        step: '0006'
      }
    ],
    duration: '/'
  },
  {
    title: '验证闭环',
    status: '0004',
    operations: [
      {
        operation: '回归问题',
        componentName: 'ReturnIssueProblem',
        isCurrent: false,
        isSelected: false,
        isFinished: false,
        data: {},
        step: '0007'
      },
      {
        operation: '更新答复口径',
        componentName: 'UpdateResponseKnow',
        isCurrent: false,
        isSelected: false,
        isFinished: false,
        data: {},
        step: '0008'
      }
    ],
    duration: '/'
  }
]