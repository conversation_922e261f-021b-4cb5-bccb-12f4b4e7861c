<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/issues-opinion-manage' }"
        >问题管理</el-breadcrumb-item
      >
      <el-breadcrumb-item>{{ desc }}</el-breadcrumb-item>
    </el-breadcrumb>
    <el-tabs v-model="activeName" class="demo-tabs mt-12 tabs-content">
      <el-tab-pane label="基本信息" name="基本信息">
        <BaseDetail :detail-data="baseDetail" />
      </el-tab-pane>
      <el-tab-pane label="进展信息" name="进展信息">
        <ProblemProgress :key-info="keyInfo" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref } from "vue";
import BaseDetail from "./baseDetail/index.vue";
import ProblemProgress from "./progressInfo/index-new.vue";
import { useRoute } from "vue-router";
import { fetchIssueDetail } from "@/api/view/optionDetail/detail";
import dayjs from "dayjs";
import { translate } from "element-plus";
import {
  statusList,
  transValue2Label,
} from "@/components/issuesOpinionManage/commonArea/checkList";
import { useEventBus } from "@/hooks/useEventBus";
const activeName = ref("基本信息");
const router = useRoute();
const opinionIssueId = ref(router.query.id);
const desc = ref("");
const eventBus = useEventBus();
const getIssueDetail = async () => {
  const data = await fetchIssueDetail(opinionIssueId.value);
  desc.value = data.description;
  transferDataToBaseDetail(data);
  transferKeyInfo(data);
};

const formatStr = (data, unit = "") => {
  if (data) {
    return data + unit;
  } else {
    return "---";
  }
};

const formatDate = (date) => {
  if (date && date.length > 0) {
    return dayjs(new Date(date)).format("YYYY-MM-DD");
  } else {
    return "---";
  }
};

const formatStatusValue = (data, isAbbr = false) => {
  if (!isAbbr) {
    if (data > 0) {
      return "是";
    } else {
      return "否";
    }
  } else {
    return data > 0 ? "Y" : "N";
  }
};

const baseDetail = ref<any>([]);

const keyInfo = ref<any>([]);

const transferDataToBaseDetail = (data) => {
  baseDetail.value = [
    [
      {
        type: "基本信息",
        title: "问题单号",
        value: formatStr(data.opinionIssueId),
      },
      {
        type: "基本信息",
        title: "登记日期",
        value: formatDate(data.createTime),
      },
      {
        type: "基本信息",
        title: "问题描述",
        value: formatStr(data.description),
      },
      {
        type: "基本信息",
        title: "L3/6功能",
        prop: "levelFunction",
        defaultValue: data.levelFunction,
        value: formatStr(data.levelFunction),
      },
      {
        type: "基本信息",
        title: "主责任方",
        value: formatStr(data.issueAttribution),
      },
      {
        type: "基本信息",
        title: "优先级",
        prop: "priority",
        defaultValue: data.priority,
        value: formatStr(data.priority),
      },
      {
        type: "基本信息",
        title: "来源",
        value: formatStr(data.source),
      },
      {
        type: "基本信息",
        title: "汇总声量值",
        value: formatStr(data.totalVolume.toString()),
      },
      {
        type: "基本信息",
        title: "答复口径",
        value: formatStr(data.weknowId),
      },
      {
        type: "基本信息",
        title: "FUT声量值",
        value: formatStr(data.futVolume),
      },
      {
        type: "基本信息",
        title: "BetaClub声量值",
        value: formatStr(data.betaclubVolume),
      },
      {
        type: "基本信息",
        title: "AG声量值",
        value: formatStr(data.agVolume),
      },
      {
        type: "基本信息",
        title: "产品机型",
        value: formatStr(data.productModel),
      },
      {
        type: "基本信息",
        title: "严重程度",
        value: formatStr(data.severity),
      },
      {
        type: "基本信息",
        title: "当前阶段",
        value: formatStr(transValue2Label(data.status, statusList)),
      },
      {
        type: "基本信息",
        title: "问题提出人",
        value: formatStr(data.reportedPerson),
      },
      {
        type: "基本信息",
        prop: "responsibleTeam",
        title: "责任团队/TL",
        defaultValue: data.responsibleTeam,
        value: formatStr(data.responsibleTeam),
      },
      {
        type: "基本信息",
        prop: "owner",
        title: "责任人",
        defaultValue: data.owner,
        value: formatStr(data.owner),
      },
      {
        type: "基本信息",
        title: "建议解决时间",
        value: formatDate(data.suggestResolveTime),
      },
      {
        type: "基本信息",
        title: "备注",
        prop: "remark",
        defaultValue: data.remark,
        value: formatStr(data.remark),
      },
    ],
    [
      {
        type: "问题处理信息",
        title: "来源是否闭环",
        value: formatStatusValue(data.sourceClosed),
      },
      {
        type: "问题处理信息",
        title: "关联单详情",
        value: formatStr(data.childIssue),
      },
      {
        type: "问题处理信息",
        title: "问题分类",
        value: formatStr(data.faultCategory),
      },
      {
        type: "问题处理信息",
        title: "最高级别",
        value: formatStr(data.highestLevel),
      },
      {
        type: "问题处理信息",
        title: "问题进展",
        value: formatStr(data.highestLevel),
      },
      {
        type: "问题处理信息",
        title: "计划解决时间",
        value: formatDate(data.planSolveTime),
      },
      {
        type: "问题处理信息",
        title: "当前级别",
        value: formatStr(data.opinionIssueLevel),
      },
      {
        type: "问题处理信息",
        title: "定界超期标记",
        value: formatStatusValue(data.delimitationOverdue, true),
      },
      {
        type: "问题处理信息",
        title: "故障知识ID",
        value: formatStr(data.faultKnowledgeId),
      },
    ],
    [
      {
        type: "责任人信息",
        title: "EWP责任人",
        value: formatStr(data.ewpOwner),
      },
      {
        type: "责任人信息",
        title: "当前处理人",
        value: formatStr(data.currentHandler),
      },
      {
        type: "责任人信息",
        title: "问题反馈人",
        value: formatStr(data.reportedPerson),
      },
      {
        type: "责任人信息",
        title: "问题创建人",
        value: formatStr(data.createdPerson),
      },
      {
        type: "责任人信息",
        title: "定界责任人",
        value: formatStr(data.ewpOwner),
      },
      {
        type: "责任人信息",
        title: "应用PM",
        value: formatStr(data.appPm),
      },
      {
        type: "责任人信息",
        title: "DTSE TL",
        value: formatStr(data.dtseLeader),
      },
      {
        type: "责任人信息",
        title: "DTSE责任人",
        value: formatStr(data.dtseOwner),
      },
      {
        type: "责任人信息",
        title: "BD TL",
        value: formatStr(data.bdLeader),
      },
      {
        type: "责任人信息",
        title: "BD 责任人",
        value: formatStr(data.bdOwner),
      },
      {
        type: "责任人信息",
        title: "生态解决方案TL",
        value: formatStr(data.solutionLeader),
      },
      {
        type: "责任人信息",
        title: "生态解决方案责任人",
        value: formatStr(data.solutionOwner),
      },
    ],
    [
      {
        type: "关键时间点",
        title: "定界时间",
        value: formatDate(data.delimitFinishTime),
      },
      {
        type: "关键时间点",
        title: "锁定时间",
        value: formatDate(data.planLockTime),
      },
      {
        type: "关键时间点",
        title: "问题关闭时间",
        value: formatDate(data.closeTime),
      },
      {
        type: "关键时间点",
        title: "定界耗时",
        value: formatStr(data.delimitationCostTime, "天"),
      },
      {
        type: "关键时间点",
        title: "修复耗时",
        value: formatStr(data.repairCostTime, "天"),
      },
      /* {
        type: "关键时间点",
        title: "感知耗时",
        value: formatStr(data.perceiveCostTime, "天"),
      },*/
    ],
    [
      {
        type: "应用信息",
        title: "纵队",
        value: formatStr(data.team),
      },
      {
        type: "应用信息",
        title: "归属代表处/系统部",
        value: formatStr(data.represent),
      },
      {
        type: "应用信息",
        title: "应用名称",
        value: formatStr(data.appName),
      },
    ],
  ];
};

const transferKeyInfo = (data) => {
  keyInfo.value = [
    {
      label: "问题单号",
      value: formatStr(data.opinionIssueId),
      type: "link",
    },
    {
      label: "创建日期",
      value: formatDate(data.createTime),
    },
    {
      label: "EWP责任人",
      value: formatStr(data.ewpOwner),
    },
    {
      label: "主责任方",
      value: formatStr(data.issueAttribution),
    },
    {
      label: "归属代表处/系统部",
      value: formatStr(data.represent),
    },
    {
      label: "应用名称",
      value: formatStr(data.appName),
    },
    {
      label: "场景名称",
      value: formatStr(data.sceneName),
    },
    {
      label: "问题描述",
      value: formatStr(data.description),
    },
    {
      label: "问题等级",
      value: formatStr(data.opinionIssueLevel),
    },
    {
      label: "优先级",
      value: formatStr(data.priority),
    },
    {
      label: "问题来源",
      value: formatStr(data.source),
    },
    {
      label: "汇总声量",
      value: formatStr(data.totalVolume),
      sceneId: data.sceneId,
      sceneName: data.sceneName,
    },
    {
      label: "知识ID",
      value: formatStr(data.weknowId),
    },
    {
      label: "工单状态",
      value: formatStr(transValue2Label(data.status, statusList)),
    },
    {
      label: "当前处理人",
      value: formatStr(data.currentHandler),
    },
    {
      label: "产品机型",
      value: formatStr(data.productModel),
    },
    {
      label: "计划解决时间",
      value: formatDate(data.planSolveTime),
    },
  ];
};

onMounted(() => {
  const type = ref(router.query.type as string);
  if (type.value === "info") {
    activeName.value = "基本信息";
  } else {
    activeName.value = "进展信息";
  }
  getIssueDetail();
  eventBus.on("getIssueInfo", async (callBack?: Function) => {
    await getIssueDetail();
    callBack && callBack();
  });
});
onBeforeUnmount(() => {
  eventBus.off("getIssueInfo");
});
</script>

<style lang="less">
.el-tabs__item.is-active {
  font-size: 18px;
}
.el-tabs__header {
  margin-bottom: 20px;
}
</style>
